<?php

namespace App\Http\Controllers\MasterMaintenance;

use App\Http\Controllers\Controller;
use App\Allocation;
use App\AllocationLocation;
use App\BackgroundTask;
use App\CustomerOrderItem;
use App\CustomerOrder;
use App\DataTables\Master\AllocationDataTable;
use App\Item;
use App\ItemLoc;
use App\Jobs\BackgroundTasksQueue;
use App\LotLoc;
use App\Services\AllocationService;
use App\Services\OverrideQtyService;
use App\SiteSetting;
use App\UomConv;
use App\Picklist;
use App\View\TparmView;
use Carbon\Carbon;
use Illuminate\Http\Request;
use DB;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Illuminate\Validation\ValidationException;
use Yajra\DataTables\Facades\DataTables;
use App\Services\ExportService;

class AllocationController extends Controller
{
    private $name = 'MAINTENANCE_ALLOCATION'; // 'Allocation';

    public function __construct()
    {
        $this->middleware('auth');
        //$this->middleware('can:hasCoReturn');
        $this->middleware('can:hasAllocationMaintenance');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(AllocationDataTable $allocationDataTable)
    {


        if (!\Gate::allows('hasAllocationMaintenance')) {
            return view('errors.404v2')->with('page', 'error');
        }
        Allocation::where('suffix', '0000')->where('order_type', 'Customer Order')->orwhere('order_type', 'Transfer Order')->update(array('suffix' => ''));
        Allocation::where('order_type', 'Job Order')->where('suffix', '')->orWhereNull('suffix')->update(array('suffix' => '0000'));
        return $allocationDataTable->render('MasterMaintenance.allocation.index');
    }

    public function export(AllocationDataTable $allocationDataTable)
    {
        $export = new ExportService($allocationDataTable, $this->name, "Allocations");
        return $export->handleExport();
    }


    public function errorshow(Request $request)
    {
        //$this->middleware('Illuminate\Http\FrameGuard');
        //$this->removeMiddleware('Illuminate\Http\FrameGuard');
        //        config(['app.debug' => true]);

        if (!\Gate::allows('hasAllocationMaintenance')) {
            return view('errors.404v2')->with('page', 'error');
        }

        $url = base64_decode($request->url);
        // dd($url);
        if (filter_var($url, FILTER_VALIDATE_URL) === FALSE) {
            $url =  \App\Services\GeneralService::getStorage()->url($url);
        }

        $url = \App\Services\GeneralService::url_path_encode($url);


        // $content = file_get_contents($url);
        //    dd($content);
        $content = explode("\n", file_get_contents($url));

        //                       config(['app.debug' => fals]);

        // dd($content);
        // exit;
        return view('MasterMaintenance.allocation.error')->with('url', $url)->with('content', $content);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    // Generate available item for pick list
    public function generate(Request $request)
    {

        $siteSettings = new SiteSetting;
        $job = $co = $to = $results = null;
        $allocations = new Allocation;

        // Avoid selecting the same items if status is Open, Pending, or Generated.
        // Only Completed or Failed can be allocated again
        $pickedItems = $allocations->where('order_type', 'Job Order')
            ->where(function ($q) {
                $q->where('status', 'Open')
                    ->orWhere('status', 'Pending')
                    ->orWhere('status', 'Generated');
            })
            ->pluck('ref_id')
            ->toArray();

        // dd($test);
        if (in_array('Job Order', request('type'))) {
            // Find filtered Job
            $job = DB::query()->from('jobmatlitemviewallocation')
                // ->select(
                //     'job_matls.id',
                //     DB::raw('job_matls.id as ref_id'),
                //     'job_matls.whse_num',
                //     'job_matls.job_num as ref_num',
                //     'job_matls.suffix as suffix',
                //     'job_matls.oper_num as ref_line',
                //     'job_matls.sequence as ref_release',
                //     'job_matls.matl_item as item_num',
                //     'items.item_desc',
                //     DB::raw('DATE_FORMAT(jobs.start_date_plan, "' . $siteSettings->getMySQLDateFormat() . '") as due_date'),
                //     DB::raw('IFNULL(qty_required,0) - IFNULL(qty_issued,0) as qty_required'),
                //     DB::raw('NULL as qty_available'),
                //     DB::raw('NULL as qty_shipped'),
                //     'jobs.qty_released as qty_released',
                //     'job_matls.uom',
                //     'items.product_code',
                //     'jobs.cust_num',
                //     DB::raw('NULL as shipping_zone'),
                //     DB::raw('"Job Order" as type'),
                // )
                ->whereNotIn('jobmatlitemviewallocation.id', $pickedItems);
            // ->join('items', function ($join) {
            //     $join->on('items.item_num', '=', 'job_matls.matl_item')
            //         ->on('items.site_id', '=', 'job_matls.site_id');
            // })
            // ->join('jobs', function ($join) {
            //     $join->on('jobs.job_num', 'job_matls.job_num')
            //         ->on('jobs.site_id', 'job_matls.site_id');
            // });

            // Filter warehouse
            if (request('whse_num')) {
                $job->where('jobmatlitemviewallocation.whse_num', request('whse_num'));
            }

            // Filter order number
            if (request('from_order_num') && request('to_order_num')) {
                $job->whereBetween('jobmatlitemviewallocation.job_num', [request('from_order_num'), request('to_order_num')]);
            } else if (request('from_order_num')) {
                $job->where('jobmatlitemviewallocation.job_num', '>=', request('from_order_num'));
            } else if (request('to_order_num')) {
                $job->where('jobmatlitemviewallocation.job_num', '<=', request('to_order_num'));
            }

            // Filter due date
            if (request('from_due_date') && request('to_due_date')) {
                $from_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('from_due_date'))->format('Y-m-d 00:00:00');
                $to_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('to_due_date'))->format('Y-m-d 23:59:59');
                $job->whereBetween('jobmatlitemviewallocation.start_date_plan', [$from_due_date, $to_due_date]);
            } else if (request('from_due_date')) {
                $from_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('from_due_date'))->format('Y-m-d 00:00:00');
                $job->where('jobmatlitemviewallocation.start_date_plan', '>=', $from_due_date);
            } else if (request('to_due_date')) {
                $to_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('to_due_date'))->format('Y-m-d 23:59:59');
                $job->where('jobmatlitemviewallocation.start_date_plan', '<=', $to_due_date);
            }

            // Filter Item
            if (request('from_item_num') && request('to_item_num')) {
                $job->whereBetween('jobmatlitemviewallocation.matl_item', [request('from_item_num'), request('to_item_num')]);
            } else if (request('from_item_num')) {
                $job->where('jobmatlitemviewallocation.matl_item', '>=', request('from_item_num'));
            } else if (request('to_item_num')) {
                $job->where('jobmatlitemviewallocation.matl_item', '<=', request('to_item_num'));
            }

            // Filter customer
            if (request('from_cust_num') && request('to_cust_num')) {
                $job->whereBetween('jobmatlitemviewallocation.cust_num', [request('from_cust_num'), request('to_cust_num')]);
            } else if (request('from_cust_num')) {
                $job->where('jobmatlitemviewallocation.cust_num', '>=', request('from_cust_num'));
            } else if (request('to_cust_num')) {
                $job->where('jobmatlitemviewallocation.cust_num', '<=', request('to_cust_num'));
            }

            // Filter product code
            if (request('from_product_code') && request('to_product_code')) {
                $job->whereBetween('jobmatlitemviewallocation.product_code', [request('from_product_code'), request('to_product_code')]);
            } else if (request('from_product_code')) {
                $job->where('jobmatlitemviewallocation.product_code', '>=', request('from_product_code'));
            } else if (request('to_product_code')) {
                $job->where('jobmatlitemviewallocation.product_code', '<=', request('to_product_code'));
            }

            $job->where('jobmatlitemviewallocation.site_id', auth()->user()->site_id);
            // $job->where('jobs.job_status', 'R');

            // $job->whereRaw('IFNULL(qty_required,0) - IFNULL(qty_issued,0) > 0');
        }

        if (in_array('Customer Order', request('type'))) {

            // Avoid selecting the same items if status is Open, Pending, or Generated.
            // Only Completed or Failed can be allocated again
            $pickedItems = $allocations->where('order_type', 'Customer Order')
                ->where(function ($q) {
                    $q->where('status', 'Open')
                        ->orWhere('status', 'Pending')
                        ->orWhere('status', 'Generated');
                })
                ->pluck('ref_id')
                ->toArray();

            // Find filtered of CO
            $dev = $request->dev ?? false;
            $coView = $dev ? "coitemviewallocation_opt" : "coitemviewallocation_simple";
            $co = DB::query()->from($coView)
                // ->select(
                //     'coitems.id',
                //     DB::raw('coitems.id as ref_id'),
                //     'coitems.whse_num',
                //     'coitems.co_num as ref_num',
                //     DB::raw('NULL as suffix'),
                //     'coitems.co_line as ref_line',
                //     'coitems.co_rel as ref_release',
                //     'coitems.item_num',
                //     'items.item_desc',
                //     DB::raw('DATE_FORMAT(coitems.due_date, "' . $siteSettings->getMySQLDateFormat() . '") as due_date'),
                //     DB::raw('IFNULL(qty_released,0) - IFNULL(qty_shipped,0) + IFNULL(qty_returned,0) as qty_required'),
                //     DB::raw('NULL as qty_available'),
                //     'coitems.qty_shipped as qty_shipped',
                //     'coitems.qty_released as qty_released',
                //     'coitems.uom',
                //     'items.product_code',
                //     'coitems.cust_num',
                //     'coitems.shipping_zone',
                //     DB::raw('"Customer Order" as type'),
                //     // DB::raw('1 as isChecked')
                // )
                ->whereNotIn('' . $coView . '.id', $pickedItems);
            // ->join('items', function ($join) {
            //     $join->on('items.item_num', '=', ''.$coView.'.item_num')
            //         ->on('items.site_id', '=', ''.$coView.'.site_id');
            // });

            // Filter warehouse
            if (request('whse_num')) {
                $co->where('' . $coView . '.whse_num', request('whse_num'));
            }

            // Filter order number
            if (request('from_order_num') && request('to_order_num')) {
                $co->whereBetween('' . $coView . '.co_num', [request('from_order_num'), request('to_order_num')]);
            } else if (request('from_order_num')) {
                $co->where('' . $coView . '.co_num', '>=', request('from_order_num'));
            } else if (request('to_order_num')) {
                $co->where('' . $coView . '.co_num', '<=', request('to_order_num'));
            }

            // Filter due date
            if (request('from_due_date') && request('to_due_date')) {
                $from_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('from_due_date'))->format('Y-m-d 00:00:00');
                $to_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('to_due_date'))->format('Y-m-d 23:59:59');
                $co->whereBetween('' . $coView . '.due_date', [$from_due_date, $to_due_date]);
            } else if (request('from_due_date')) {
                $from_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('from_due_date'))->format('Y-m-d 00:00:00');
                $co->where('' . $coView . '.due_date', '>=', $from_due_date);
            } else if (request('to_due_date')) {
                $to_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('to_due_date'))->format('Y-m-d 23:59:59');
                $co->where('' . $coView . '.due_date', '<=', $to_due_date);
            }

            // Filter Item
            if (request('from_item_num') && request('to_item_num')) {
                $co->whereBetween('' . $coView . '.item_num', [request('from_item_num'), request('to_item_num')]);
            } else if (request('from_item_num')) {
                $co->where('' . $coView . '.item_num', '>=', request('from_item_num'));
            } else if (request('to_item_num')) {
                $co->where('' . $coView . '.item_num', '<=', request('to_item_num'));
            }

            // Filter customer
            if (request('from_cust_num') && request('to_cust_num')) {
                $co->whereBetween('' . $coView . '.cust_num', [request('from_cust_num'), request('to_cust_num')]);
            } else if (request('from_cust_num')) {
                $co->where('' . $coView . '.cust_num', '>=', request('from_cust_num'));
            } else if (request('to_cust_num')) {
                $co->where('' . $coView . '.cust_num', '<=', request('to_cust_num'));
            }

            // Filter product code
            if (request('from_product_code') && request('to_product_code')) {
                $co->whereBetween('' . $coView . '.product_code', [request('from_product_code'), request('to_product_code')]);
            } else if (request('from_product_code')) {
                $co->where('' . $coView . '.product_code', '>=', request('from_product_code'));
            } else if (request('to_product_code')) {
                $co->where('' . $coView . '.product_code', '<=', request('to_product_code'));
            }

            $co->where('' . $coView . '.site_id', auth()->user()->site_id);
            // $co->where(''.$coView.'.rel_status', '!=', 'C');
            // $co->whereRaw('IFNULL(qty_released,0) - IFNULL(qty_shipped,0) + IFNULL(qty_returned,0) > 0');
        }

        // Query TO if no filter by customer
        if (!request('from_cust_num') && !request('to_cust_num')) {
            if (in_array('Transfer Order', request('type'))) {

                // Avoid selecting the same items if status is Open, Pending, or Generated.
                // Only Completed or Failed can be allocated again
                $pickedItems = $allocations->where('order_type', 'Transfer Order')
                    ->where(function ($q) {
                        $q->where('status', 'Open')
                            ->orWhere('status', 'Pending')
                            ->orWhere('status', 'Generated');
                    })
                    ->pluck('ref_id')
                    ->toArray();

                // Find filtered of TO
                $to = DB::query()->from('toitemview')
                    // ->select(
                    //     'transfer_lines.id',
                    //     DB::raw('transfer_lines.id as ref_id'),
                    //     'transfer_orders.from_whse as whse_num',
                    //     'transfer_lines.trn_num as ref_num',
                    //     DB::raw('NULL as suffix'),
                    //     'transfer_lines.trn_line as ref_line',
                    //     DB::raw('NULL as ref_release'),
                    //     'transfer_lines.item_num',
                    //     'items.item_desc',
                    //     DB::raw('DATE_FORMAT(transfer_orders.schedule_ship_date, "' . $siteSettings->getMySQLDateFormat() . '") as due_date'),
                    //     DB::raw('IFNULL(qty_required,0) - IFNULL(qty_shipped,0) as qty_required'),
                    //     DB::raw('NULL as qty_available'),
                    //     'transfer_lines.qty_shipped as qty_shipped',
                    //     DB::raw('NULL as qty_released'),
                    //     'transfer_lines.uom',
                    //     'items.product_code',
                    //     DB::raw('NULL as cust_num'),
                    //     DB::raw('NULL as shipping_zone'),
                    //     DB::raw('"Transfer Order" as type'),
                    //     // DB::raw('1 as isChecked')
                    // )
                    ->whereNotIn('toitemview.id', $pickedItems);
                // ->join('transfer_orders', function ($join) {
                //     $join->on('transfer_orders.trn_num', '=', 'transfer_lines.trn_num')
                //         ->on('transfer_orders.site_id', '=', 'transfer_lines.site_id');
                // })
                // ->join('items', function ($join) {
                //     $join->on('items.item_num', '=', 'transfer_lines.item_num')
                //         ->on('items.site_id', '=', 'transfer_lines.site_id');
                // });

                // Filter warehouse
                // TO have from_whse and to_whse; currently this filter is for from_whse
                if (request('whse_num')) {
                    $to->where('toitemview.from_whse', request('whse_num'));
                }

                // Filter order number
                if (request('from_order_num') && request('to_order_num')) {
                    $to->whereBetween('toitemview.trn_num', [request('from_order_num'), request('to_order_num')]);
                } else if (request('from_order_num')) {
                    $to->where('toitemview.trn_num', '>=', request('from_order_num'));
                } else if (request('to_order_num')) {
                    $to->where('toitemview.trn_num', '<=', request('to_order_num'));
                }

                // Filter due date
                if (request('from_due_date') && request('to_due_date')) {
                    $from_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('from_due_date'))->format('Y-m-d 00:00:00');
                    $to_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('to_due_date'))->format('Y-m-d 23:59:59');
                    $to->whereBetween('toitemview.schedule_ship_date', [$from_due_date, $to_due_date]);
                } else if (request('from_due_date')) {
                    $from_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('from_due_date'))->format('Y-m-d 00:00:00');
                    $to->where('toitemview.schedule_ship_date', '>=', $from_due_date);
                } else if (request('to_due_date')) {
                    $to_due_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('to_due_date'))->format('Y-m-d 23:59:59');
                    $to->where('toitemview.schedule_ship_date', '<=', $to_due_date);
                }

                // Filter Item
                if (request('from_item_num') && request('to_item_num')) {
                    $to->whereBetween('toitemview.item_num', [request('from_item_num'), request('to_item_num')]);
                } else if (request('from_item_num')) {
                    $to->where('toitemview.item_num', '>=', request('from_item_num'));
                } else if (request('to_item_num')) {
                    $to->where('toitemview.item_num', '<=', request('to_item_num'));
                }

                // Filter product code
                if (request('from_product_code') && request('to_product_code')) {
                    $to->whereBetween('toitemview.product_code', [request('from_product_code'), request('to_product_code')]);
                } else if (request('from_product_code')) {
                    $to->where('toitemview.product_code', '>=', request('from_product_code'));
                } else if (request('to_product_code')) {
                    $to->where('toitemview.product_code', '<=', request('to_product_code'));
                }

                // $to->where('transfer_lines.line_stat', '!=', 'C');
                $to->where('toitemview.site_id', auth()->user()->site_id);

                // $to->whereRaw('IFNULL(qty_required,0) - IFNULL(qty_shipped,0) > 0');
            }
        }

        if (in_array('Job Order', request('type'))) {
            $results = $job;
        }
        if (in_array('Customer Order', request('type'))) {
            if ($results) {
                $results = $results->union($co);
            } else {
                $results = $co;
            }
        }
        // Show / Union with TO if no filter by customer
        if (!request('from_cust_num') && !request('to_cust_num')) {
            if (in_array('Transfer Order', request('type'))) {
                if ($results) {
                    $results = $results->union($to);
                } else {
                    $results = $to;
                }
            }
        }

        $results =  $results->where('item_num', '!=', 'NON-INV')->groupBy('ref_id');

        if ($results) {
            // Order By Due Date; set null Due Date to last
            $results->orderByRaw('ISNULL(due_date), STR_TO_DATE(due_date,"' . $siteSettings->getMySQLDateFormat() . '") ASC');
            // $results = $results->get();

            // // Get Qty Available
            // if (count($results) > 0) {

            //     // Get parameter value of allow_expired_item_Picklist
            //     $tparm = new TparmView;
            //     $allow_expired_item_Picklist = $tparm->getTparmValue('System', 'allow_expired_item_Picklist');

            //     $itemloc = new ItemLoc();
            //     $lotloc = new LotLoc();
            //     $item = new Item();

            //     foreach ($results as $key => $result) {
            //         // Find qty available
            //

            //         // Change qty required to base UOM's value
            //         $uom_conv = UomConv::convert($result->uom, $result->qty_required, $result->item_num, $result->cust_num, null, null);
            //         $result->qty_required = number_format($uom_conv['qty'], 5, '.', '');

            //         // Change qty shipped to base UOM's value
            //         $uom_conv = UomConv::convert($result->uom, $result->qty_shipped, $result->item_num, $result->cust_num, null, null);
            //         $result->qty_shipped = number_format($uom_conv['qty'], 5, '.', '');

            //         // Change qty released to base UOM's value
            //         $uom_conv = UomConv::convert($result->uom, $result->qty_released, $result->item_num, $result->cust_num, null, null);
            //         $result->qty_released = number_format($uom_conv['qty'], 5, '.', '');

            //         // Get Qty Shortage
            //         $result->qty_shortage = max(number_format($result->qty_required - ($dataitemloclotloc->sum_qty_available ?? 0), 5, '.', ''), number_format(0, 5, '.', ''));

            //         // Change to base UOM
            //         if ($dataitemloclotloc) {
            //             $result->uom = $dataitemloclotloc->uom;
            //         }
            //         // Remove data from generate if criteria not fulfilled (example: location's status is inactive or type is not Stock)
            //         // else {
            //         //     $results->forget($key);
            //         // }
            //     }
            // }
            return DataTables::of($results)

                ->editColumn(
                    'qty_available',
                    function ($result) {
                        // dd($result);
                        $qty_available= getAllocationQtyAvailable($result);


                        // $qty_available = ($dataitemloclotloc->sum_qty_available ?? number_format(0, 5, '.', ''));
                        // return 0;
                        $uom_conv = UomConv::convert($result->uom, $qty_available, $result->item_num, $result->cust_num, null, null);
                       return $uom_conv['qty'];
                        // return number_format($uom_conv['qty'], 5, '.', '');
                    }
                )
                ->editColumn(
                    'due_date',
                    function ($result) use ($siteSettings) {
                        // return $result->due_date;
                        if ($result->due_date == "" || $result->due_date == null)
                            return "";
                        return Carbon::parse($result->due_date)->format(SiteSetting::getOutputDateFormat());
                    }
                )
                ->editColumn(
                    'qty_required',
                    function ($result) {
                    // $uom_conv = UomConv::convert($result->uom, $result->qty_required, $result->item_num, $result->cust_num, null, null);
                    // return number_format($uom_conv['qty'], 5, '.', '');
                    return number_format($result->qty_required, 5, '.', '');

                    }
                )
                ->editColumn(
                    'qty_released',
                    function ($result) {


                    // Change qty released to base UOM's value
                    // $uom_conv = UomConv::convert($result->uom, $result->qty_released, $result->item_num, $result->cust_num, null, null);
                    // return number_format($uom_conv['qty'], 5, '.', '');
                    return number_format($result->qty_released, 5, '.', '');
                    }
                )
                ->editColumn(
                    'qty_shipped',
                    function ($result) {


                    // Change qty shipped to base UOM's value
                    // $uom_conv = UomConv::convert($result->uom, $result->qty_shipped, $result->item_num, $result->cust_num, null, null);
                    // return number_format($uom_conv['qty'], 5, '.', '');
                    return number_format($result->qty_shipped, 5, '.', '');

                    }
                )
                // ->editColumn(
                //     'qty_shortage',
                //     function ($result) {
                //     if ($result->qty_shortage){
                //         $qty_shortage=$result->qty_shortage;
                //     }else{
                //         $qty_shortage= $result->qty_required-$result->qty_available;
                //     }
                //         // return $result->qty_available;
                //         $uom_conv = UomConv::convert($result->uom, $qty_shortage, $result->item_num, $result->cust_num, null, null);
                //         return number_format($uom_conv['qty'], 5, '.', '');
                //     }
                // )
                // ->filterColumn(
                //     'cust_num',
                //     function ($query, $keyword) {
                //         dd($keyword);
                //         return $query->where('cust_num', "LIKE", $keyword);
                //     }
                // )
                ->make(true);
        }

        // No data available
        $results = [
            'recordsTotal' => 0,
            'recordsFiltered' => 0,
            'data' => [],
        ];

        return DataTables::of($results)->make(true);
    }

    public function create()
    {
        $plan = SiteSetting::select('plan_id')->where('site_id', auth()->user()->site_id)->where('status', 1)->value('plan_id');

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $enable_warehouse = $tparm->getTparmValue('Allocation', 'enable_warehouse');
        $allocation_groups = auth()->user()->groups()->whereIn('code', ['MAINTENANCE_ALLOCATION_CO', 'MAINTENANCE_ALLOCATION_TO', 'MAINTENANCE_ALLOCATION_JO', 'MAINTENANCE_ALLOCATION'])->pluck('code')->toArray();
        $plan_code = SiteSetting::where('site_id', auth()->user()->site_id)->value('plan_code');

        if (config('icapt.special_modules.enable_suffix')) {
            return view('MasterMaintenance.allocation-new.add', compact('enable_warehouse', 'allocation_groups'))->with('unit_quantity_format', $unit_quantity_format)->with('plan', $plan)->with('plan_code', $plan_code);
        } else {
            return view('MasterMaintenance.allocation.add', compact('enable_warehouse', 'allocation_groups'))->with('unit_quantity_format', $unit_quantity_format)->with('plan', $plan)->with('plan_code', $plan_code);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // dd($request->allocation);
        if ($request->allocation) {
            $allocationsData=$request->allocation;
            $ids= array_keys($allocationsData);
            $dev= $request->dev;
            $coView = $dev ? "coitemviewallocation_opt" : "coitemviewallocation_simple";
            $allocations = DB::query()->from('' . $coView . '')->whereIn('id', $ids)->get();
            $errors = 0;
            $error_msg = __('error.admin.qty_variance_allocate', ['resource' => "Order Item"]);
            foreach ($allocations as $allocation ) {
                // $allocationId = json_decode($allocation);
                // dd($allocation, $allocationId);

                // $allocation = $allocations->where('id', '1629')->first();
                // dd($allocation);
                //getting qty shortage after fixing the query on filter
                $qty_available= $allocationsData[$allocation->id]??0;
                // dd($coView);
                // dd($qty_available);
                $allocation->qty_shortage = max(number_format($allocation->qty_required - $qty_available, 5, '.', ''), number_format(0, 5, '.', ''));

                if ($allocation->qty_shortage > 0) {

                    return $error_msg;
                    break;
                }
            }
            foreach ($allocations as $allocation) {
                // $allocation = json_decode($allocation);
                // dd($allocation);
                //getting qty shortage after fixing the query on filter
                $qty_available = $allocationsData[$allocation->id]??0;

                $allocation->qty_shortage = max(number_format($allocation->qty_required - ($qty_available), 5, '.', ''), number_format(0, 5, '.', ''));


                $checkExist = Allocation::where('order_type', $allocation->type)
                    ->where('ref_num', $allocation->ref_num)
                    ->where('ref_line', $allocation->ref_line)
                    ->where('ref_release', $allocation->ref_release)
                    ->first();

                if ($checkExist) {
                    // Return error message due to it already been selected by another user
                    if ($checkExist->status == "Pending") {
                        return __('error.admin.added_by', [
                            'resource1' => $checkExist->ref_num . '-' . $checkExist->ref_line . '-' . $checkExist->ref_release,
                            'resource2' => $checkExist->created_by,
                        ]);
                    }
                    // Delete it since it is going to reallocate this order line
                    else if ($checkExist->status == "Failed") {
                        $checkExist->delete();
                    }
                }
                $suffix = "";
                if (isset($allocation->suffix)) {
                    $suffix = $allocation->suffix;
                }

                if ($allocation->type == 'Customer Order') {
                    Allocation::create([
                        'order_type' => $allocation->type,
                        'whse_num' => $allocation->whse_num,
                        'item_num' => $allocation->item_num,
                        'uom' => $allocation->uom,
                        'ref_id' => $allocation->ref_id,
                        'ref_num' => $allocation->ref_num,
                        'suffix' => $suffix,
                        'ref_line' => $allocation->ref_line,
                        'ref_release' => $allocation->ref_release,
                        'due_date' => $allocation->due_date ?? null,
                        'qty_required' => $allocation->qty_required,
                        'qty_available' => $allocation->qty_available?? $qty_available,
                        'qty_shortage' => $allocation->qty_shortage,
                        'qty_shipped' => $allocation->qty_shipped,
                        'qty_released' => $allocation->qty_released,
                        'cust_num' => $allocation->cust_num,
                        'shipping_zone' => $allocation->shipping_zone,
                        'status' => 'Pending',
                    ]);
                } else
                    Allocation::create([
                        'order_type' => $allocation->type,
                        'whse_num' => $allocation->whse_num,
                        'item_num' => $allocation->item_num,
                        'uom' => $allocation->uom,
                        'ref_id' => $allocation->ref_id,
                        'ref_num' => $allocation->ref_num,
                        'suffix' => $suffix,
                        'ref_line' => $allocation->ref_line,
                        'ref_release' => $allocation->ref_release,
                        'due_date' => $allocation->due_date ?? null,
                        'qty_required' => $allocation->qty_required,
                        'qty_available' => $allocation->qty_available?? $qty_available,
                        'qty_shortage' => $allocation->qty_shortage,
                        'qty_shipped' => $allocation->qty_shipped,
                        'qty_released' => $allocation->qty_released,
                        'cust_num' => $allocation->cust_num,
                        'shipping_zone' => ' ',
                        'status' => 'Pending',
                    ]);
            }
            if ($errors)
                return $error_msg;
            return 'true';
        }
    }

    public function pending(Request $request)
    {
        // DataTable data
        if ($request->ajax()) {
            $pending = Allocation::with('item')
                ->where('status', 'Pending')
                ->where('created_by', auth()->user()->name)  // Only show pending created by its user
                ->get();

            $result = DataTables::of($pending)->make(true);
            return $result;
        }

        return view('MasterMaintenance.allocation.pending');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Allocation  $allocation
     * @return \Illuminate\Http\Response
     */
    public function show(Allocation $allocation)
    {

        $allocation->load('allocation_locations', 'customer', 'item');
        $ref = $allocation->ref;


        $allocationload = AllocationLocation::where('allocation_id', $allocation->id)->groupBy('lot_num', 'loc_num')->where('picked', 'No')->get();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');

        if (config('icapt.special_modules.enable_suffix')) {
            return view('MasterMaintenance.allocation-new.view', compact('allocation', 'ref', 'allocationload'))->with('unit_quantity_format', $unit_quantity_format)->with('total_quantity_format', $total_quantity_format);
        } else {
            return view('MasterMaintenance.allocation.view', compact('allocation', 'ref', 'allocationload'))->with('unit_quantity_format', $unit_quantity_format)->with('total_quantity_format', $total_quantity_format);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Allocation  $allocation
     * @return \Illuminate\Http\Response
     */
    public function edit(Allocation $allocation)
    {
        $allocation->load('allocation_locations', 'customer');
        $ref = $allocation->ref;
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');

        if (config('icapt.special_modules.enable_suffix')) {
            return view('MasterMaintenance.allocation-new.edit', compact('allocation', 'ref'))->with('unit_quantity_format', $unit_quantity_format)->with('total_quantity_format', $total_quantity_format);
        } else {
            return view('MasterMaintenance.allocation.edit', compact('allocation', 'ref'))->with('unit_quantity_format', $unit_quantity_format)->with('total_quantity_format', $total_quantity_format);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Allocation  $allocation
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        $allocation = Allocation::where('id', $id)->first();
        // dd($picklist);
        if (!$allocation) {
            return redirect(route('allocation.index'))->with('errormsg', __('error.mobile.notexist2', ['model' => __('admin.label.allocation'), 'resource1' => __('admin.button.update'), 'resource2' => $id]));
        }
        DB::beginTransaction();
        try {
            $qty_availables = [];

            // Update table: allocations
            $total_qty_manual_allocated = array_sum($request->qty_manual_allocated);
            $total_qty_available = array_sum($request->qty_available);

            // Update qty_shortage
            $allocation->qty_shortage = number_format($request->qty_required - $total_qty_manual_allocated, 5, '.', '');

            // Update qty_manual_allocated
            if ($allocation->qty_auto_allocated != $total_qty_manual_allocated) {
                $allocation->qty_manual_allocated = $total_qty_manual_allocated;
            } else {
                $allocation->qty_manual_allocated = null;
            }

            // Set status to Failed if qty_manual_allocated is 0
            if ($allocation->qty_manual_allocated !== null && $allocation->qty_manual_allocated == 0) {
                $allocation->status = "Failed";
            } else {
                if ($allocation->status == "Failed") {
                    $allocation->status = "Open";
                }
            }
            $allocation->save();

            // Update table: coitems
            // Reason I can't directly use $allocation->qty_allocated because this virtual column is not updated yet until DB::commit() is executed
            // So I have to use this instead, $allocation->qty_manual_allocated ?? $allocation->qty_auto_allocated, in order to get the updated value
            if ($allocation->order_type == "Customer Order") {
                $coitem = CustomerOrderItem::find($allocation->ref_id);
                if ($coitem) {
                    $coitem->qty_allocated = $allocation->qty_manual_allocated ?? $allocation->qty_auto_allocated; // qty_allocated
                    $coitem->save();
                }
            }

            // Update qty_available of the same whse_num and item_num
            if ($allocation->qty_available != $total_qty_available) {
                Allocation::where('whse_num', $allocation->whse_num)
                    ->where('item_num', $allocation->item_num)
                    ->update([
                        'qty_available' => $total_qty_available,
                    ]);
            }

            // Update table: allocation_locations
            foreach ($request->qty_manual_allocated as $id => $qty_manual_allocated) {

                $allocation_location = AllocationLocation::find($id);
                if ($allocation_location) {
                    // Update qty_manual_allocated
                    if ($allocation_location->qty_auto_allocated != $qty_manual_allocated) {
                        $allocation_location->qty_manual_allocated = $qty_manual_allocated;
                    } else {
                        $allocation_location->qty_manual_allocated = null;
                    }
                    $allocation_location->save();

                    // Update qty_available of the same whse_num, item_num, loc_num, and lot_num
                    $builder = AllocationLocation::whereHas('allocation', function ($q) use ($allocation) {
                        $q
                            ->where('whse_num', $allocation->whse_num)
                            ->where('item_num', $allocation->item_num);
                    })
                        ->where('loc_num', $allocation_location->loc_num);

                    if ($allocation_location->lot_num) {
                        $builder
                            ->where('lot_num', $allocation_location->lot_num)
                            ->update([
                                'qty_available' => $request->qty_available[$allocation_location->id],
                            ]);
                    } else {
                        $builder
                            ->update([
                                'qty_available' => $request->qty_available[$allocation_location->id],
                            ]);
                    }

                    // Keep previous qty_allocated before update.
                    $prev_allocation_location = $allocation_location->getOriginal('qty_allocated');

                    // Get updated qty available; need to reassign back
                    $allocation_location = AllocationLocation::find($id);

                    // Get qty available of each whse_num, item_num, loc_num, and lot_num. Will be used to update item locs / lot locs qty allocated later on
                    $qty_availables[$allocation->whse_num . '!-!' . $allocation->item_num . '!-!' . $allocation_location->loc_num . '!-!' . $allocation_location->lot_num] = $allocation_location->qty_available;

                    // Record changes of qty allocated to value change log
                    if ($allocation_location->qty_allocated != $prev_allocation_location) {
                        OverrideQtyService::newAllocationOverRideHistory(
                            __('admin.label.allocation'),
                            'Qty Allocated',
                            $prev_allocation_location,
                            $allocation_location->qty_allocated,
                            $allocation->ref_num,
                            $allocation->ref_line,
                            null,
                            $allocation_location->loc_num,
                            $allocation_location->lot_num
                        );
                    }
                }
            }

            // Update item locs / lot locs's qty allocated
            if (count($qty_availables) > 0) {
                foreach ($qty_availables as $itemlocs_lotlocs => $qty_available) {
                    $data = explode('!-!', $itemlocs_lotlocs);
                    $whse_num = $data[0];
                    $item_num = $data[1];
                    $loc_num = $data[2];
                    $lot_num = $data[3];

                    $itemloc = ItemLoc::where('whse_num', $whse_num)
                        ->where('item_num', $item_num)
                        ->where('loc_num', $loc_num)
                        ->first();

                    if ($itemloc) {
                        $qty_available_difference = number_format($itemloc->qty_available - $qty_available, 5, '.', '');
                        $itemloc->qty_allocated = max(0, number_format($itemloc->qty_allocated + $qty_available_difference, 5, '.', ''));
                        $itemloc->save();
                    }

                    if ($lot_num) {
                        $lotloc = LotLoc::where('whse_num', $whse_num)
                            ->where('item_num', $item_num)
                            ->where('loc_num', $loc_num)
                            ->where('lot_num', $lot_num)
                            ->first();

                        if ($lotloc) {
                            $qty_available_difference = number_format($lotloc->qty_available - $qty_available, 5, '.', '');
                            $lotloc->qty_allocated = max(0, number_format($lotloc->qty_allocated + $qty_available_difference, 5, '.', ''));
                            $lotloc->save();
                        }
                    }
                }
            }
            DB::commit();
            return redirect()->route('allocation.index', ['processed' => $request->processed])->with('successmsg', __('success.updated', ['resource' => __('admin.label.allocation'), 'name' => $allocation->ref_num . '-' . $allocation->ref_line]));
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Allocation  $allocation
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        //dd($request->ajax());
        // Delete pending data from Add page
        if ($request->ajax()) {
            if ($request->id) {
                if (is_array($request->id)) {
                    Allocation::whereIn('id', $request->id)->delete();
                } else {
                    Allocation::find($request->id)->delete();
                }
            } else {
                Allocation::where('status', 'Pending')
                    ->where('created_by', auth()->user()->name)
                    ->delete();
            }
        } else {
            // Delete selected checkbox from Index page
            if ($request->id) {
                $ids = explode(',', $request->id);

                // Cannot Delete if status is Generated or Completed
                $statusPickList = Allocation::whereIn('id', $ids)
                    ->where(function ($q) {
                        $q->where('status', 'Generated')
                            ->orWhere('status', 'Completed');
                    })
                    ->exists();
                // Picklist Table


                $statusgetPickList = Allocation::whereIn('id', $ids)->get();
                //dd($statusgetPickList);
                // $statusgetPickList->pick_num;
                $arrPickNum = array();
                foreach ($statusgetPickList as $value) {
                    array_push($arrPickNum, $value->pick_num);
                }

                $statusPicklistResult =  Picklist::whereIn('pick_num', $arrPickNum)->get();
                if ($statusPickList == "true") {
                    throw ValidationException::withMessages([__('error.admin.error_delete_allocation')]);
                } else {

                    foreach ($statusPicklistResult as $valuepicklist) {

                        if ($valuepicklist->status == "Released") {

                            throw ValidationException::withMessages([__('error.admin.error_delete_allocation')]);
                        }
                    }
                    // $a =  $statusgetPickList->load('allocation_locations');

                    // Reset Item loc lot and item loc
                    foreach ($statusgetPickList->load('allocation_locations') as $value) {
                        $r = @$value->allocation_locations;

                        foreach ($r as $d) {

                            if ($d->lot_num) {
                                $lotloc = LotLoc::where('whse_num', $value->whse_num)
                                    ->where('item_num', $value->item_num)
                                    ->where('loc_num', $d->loc_num)
                                    ->where('lot_num', $d->lot_num)
                                    ->first();
                                if ($lotloc) {
                                    $lotloc->qty_allocated = max(0, number_format($lotloc->qty_allocated - $d->qty_auto_allocated, 5, '.', '') < 0 ? null : number_format($lotloc->qty_allocated - $d->qty_auto_allocated, 5, '.', ''));
                                    $lotloc->save();
                                }
                            } else {
                                $itemloc = ItemLoc::where('whse_num', $value->whse_num)
                                    ->where('item_num', $value->item_num)
                                    ->where('loc_num', $d->loc_num)
                                    ->first();

                                if ($itemloc) {
                                    $itemloc->qty_allocated = max(0, number_format($itemloc->qty_allocated - $d->qty_auto_allocated, 5, '.', '') < 0 ? null : number_format($itemloc->qty_allocated - $d->qty_auto_allocated, 5, '.', ''));
                                    $itemloc->save();
                                }
                            }
                        }
                    }
                    Allocation::destroy($ids);

                    //dd($statusgetPickList->load('allocation_locations'));

                    /*if (count($statusgetPickList->allocation_locations) > 0) {
                    foreach ($statusgetPickList->allocation_locations as $allocation_location) {
                        // Minus the qty allocated of item locs / item lot locs based on the allocation_location's qty allocated
                        if ($allocation_location->lot_num) {
                            $lotloc = LotLoc::where('whse_num',$allocation->whse_num)
                                        ->where('item_num',$allocation->item_num)
                                        ->where('loc_num',$allocation_location->loc_num)
                                        ->where('lot_num',$allocation_location->lot_num)
                                        ->first();

                            if ($lotloc) {
                                $lotloc->qty_allocated = max(0,number_format($lotloc->qty_allocated - $allocation_location->qty_allocated,5,'.','') < 0 ? null : number_format($lotloc->qty_allocated - $allocation_location->qty_allocated,5,'.',''));


                                $lotloc->save();
                            }
                        }
                        else {
                            $itemloc = ItemLoc::where('whse_num',$allocation->whse_num)
                                        ->where('item_num',$allocation->item_num)
                                        ->where('loc_num',$allocation_location->loc_num)
                                        ->first();

                            if ($itemloc) {
                                $itemloc->qty_allocated = max(0,number_format($itemloc->qty_allocated - $allocation_location->qty_allocated,5,'.','') < 0 ? null : number_format($itemloc->qty_allocated - $allocation_location->qty_allocated,5,'.',''));
                                $itemloc->save();
                            }
                        }
                    }
                }*/
                }

                //dd($statusPicklist,$statusPickList,"ssssslll");

                return back()->with('successmsg', __('success.deleted', ['resource' => __('admin.label.allocation')]));
            } else {
                return back()->with('errormsg', __('error.admin.selectone'));
            }
        }
    }

    public function allocate(Request $request)
    {
        DB::beginTransaction();
        try {
            // Allocate from Allocation's Add page
            if ($request->pending) {

                if (Allocation::where('created_by', auth()->user()->name)->where('status', 'Pending')->exists()) {
                    // Update status to In Progress
                    DB::table('allocations')
                        ->where('site_id', auth()->user()->site_id)
                        ->where('created_by', auth()->user()->name)
                        ->where('status', 'Pending')
                        ->update([
                            'status' => 'In Progress',
                        ]);

                    // Store into table: background_tasks; set status to Waiting
                    $backgroundtask = BackgroundTask::create([
                        'task_num' => BackgroundTask::orderBy('task_num', 'desc')->value('task_num') + 1,
                        'name' => 'Allocation',
                        'desc' => 'Allocation',
                        'status' => 'Waiting',
                    ]);

                    //BackgroundTasksQueue::dispatch('Allocation',auth()->user(),null,$backgroundtask)->delay(now()->addSeconds(4));
                    AllocationService::allocate('Allocation', auth()->user(), null, $backgroundtask);
                    DB::commit();

                    return redirect()->route('allocation.index')->with('successmsg', __('success.allocation_submitted'));
                } else {
                    throw ValidationException::withMessages([__('error.admin.no_orderline_selected')]);
                }
            }
            // Allocate from Customer Order's view page
            else if ($request->customer_order) {
                $data = (object) $request->all();
                AllocationService::allocate('Customer Order', auth()->user(), $data);
                DB::commit();

                return back()->with('successmsg', __('success.allocation_submitted'));
            } else if ($request->job_order) {
            } else if ($request->transfer_order) {
            }
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
}
