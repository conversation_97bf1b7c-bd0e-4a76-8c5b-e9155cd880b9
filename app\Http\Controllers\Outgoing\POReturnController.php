<?php

namespace App\Http\Controllers\Outgoing;

use App\Services\SapCallService;
use App\Services\SapApiCallService;
use App\SAPb1\SAPClient;
use App\SAPb1\Filters\Equal;
use App\SAPb1\Service;
use App\SAPb1\Config;
use App\Services\CallHttpService;
use Illuminate\Support\Facades\Crypt;
use App\Http\Requests\Outgoing\POReturnRequest;
use App\Http\Controllers\Controller;
use App\Item;
use App\LotLoc;
use App\PurchaseOrderItem;
use App\PurchaseOrder;
use App\Services\GeneralService;
use App\Services\POService;
use App\UomConv;
use App\View\TparmView;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Session;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Validation\ValidationException;
use App\ReasonCode;
use App\AlternateBarcode;
use DB;
use Exception;
use App\Services\SiteConnectionService;
use App\GRN;
use App\GRNItem;
use App\Http\Controllers\ValidationController;
use App\SiteSetting;
use Camroncade\Timezone\Facades\Timezone;
use App\Services\CatchWeightService;

class POReturnController extends Controller
{

    use \App\Traits\HasDefaultLoc;

    public function index()
    {
        if (!\Gate::allows('hasPoReturn')) {
            return view('errors.404')->with('page', 'error');
        }

        $tparm = new TparmView();
        $enable_warehouse = $tparm->getTparmValue('POReturn', 'enable_warehouse');
        $def_return_type = $tparm->getTparmValue('POReturn', 'def_return_type');
        $def_return_type = $def_return_type ? $def_return_type : 'po';

        return view('shipping.poreturn.index', compact('enable_warehouse', 'def_return_type'));
    }

    public function showPoLine(Request $request)
    {

        // redirect to GRN list
        if (isset($request->return_type) && $request->return_type == "GRN") {
            // return redirect('/home/<USER>/po-receipt/grn-line-list')->withInput($request->all());
            // return redirect()->route('showGrnLine', $request->all(), 302);
            // return redirect()->route('showGrnLine')->withInput();
            return $this->showGrnLine($request);
            // dd('welp');
        }
        //dd($request->all());
        Session::put('request_data_poreturn', $request->all());

        // dd($request);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($request->item_num != "NON-INV") {
            $request->validate([
                'item_num' => 'nullable|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
                'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            ], [
                'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
                'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            ]);
        }

        // Send error if po_num's status is not open
        $checkPoNum = PurchaseOrderItem::where('po_num', $request->po_num)->where('whse_num', $request->whse_num)->where('rel_status', '!=', "C")->exists();
        $checkWhse = PurchaseOrderItem::where('po_num', $request->po_num)->first();
        if (!$checkPoNum) {
            if ($checkWhse->whse_num != $request->whse_num) {
                //throw ValidationException::withMessages(['co_num' => 'CO-' . $request->co_num. ' not matching with ' . $request->whse_num]);
                throw ValidationException::withMessages(['po_num' => 'PO [' . $request->po_num .  '] does not match Whse [' . $request->whse_num . ']']);
            } else {
                throw ValidationException::withMessages(['po_num' => 'PO-' . $request->po_num . ' cannot be proceed due to status is completed/closed']);
            }
        }

        Session::put('vend_do', $request->vend_do);
        $item = new Item();
        $po_rel = new PurchaseOrderItem();

        if ($request->item_num == null) {
            $po_rel = $po_rel->where('po_num', $request->po_num)->where('whse_num', $request->whse_num)->where('qty_returnable', '>', '0')->get();
        } else {
            $po_rel = $po_rel->where('po_num', $request->po_num)->where('whse_num', $request->whse_num)->where('item_num', $request->item_num)->where('qty_returnable', '>', '0')->get();
        }

        if ($po_rel->count() == 0) {
            Alert::error('Error', __('error.mobile.norecord'));
            throw ValidationException::withMessages([]);
        }

        // Redirect to the Process screen.
        if ($request->item_num && ($po_rel->count() == 1)) {
            $poitem = $po_rel->first();
            $request['po_rel'] = $poitem->po_rel;
            $request['po_line'] = $poitem->po_line;

            return redirect()->route('showPoReturnProcess', $request->all(), 303);
        }
        $po_rel = $po_rel->first();

        return view('shipping.poreturn.itemlist')->with('po_num', $request->po_num)->with('po_line', $request->po_line)->with('whse', $request->whse_num)->with('vend_num', $po_rel->vend_num)->with('item_num', $request->item_num)->with('unit_quantity_format', $unit_quantity_format);
    }

    public function backshowGRNLineReturn(Request $request)
    {
        $request->receipt_type == "GRN";
        return $this->showGrnLine($request);
    }

    public function backshowPoLine(Request $request)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($request->item_num != "NON-INV") {
            $request->validate([
                'item_num' => 'nullable|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
                'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            ], [
                'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
                'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            ]);
        }
//dd($request->all());
        Session::put('vend_do', $request->vend_do);
        $item = new Item();
        $po_rel = new PurchaseOrderItem();
        if ($request->item_num != "NON-INV") {
            $po_rel = $po_rel->where('po_num', $request->po_num)->where('whse_num', $request->whse_num)->get();
        } else {
            $po_rel = $po_rel->where('po_num', $request->po_num)->where('whse_num', $request->whse_num)->get();
        }

        // $po_rel = $po_rel->where('po_num', $request->po_num)->where('whse_num', $request->whse_num)->where('item_num', 'like', '%' . $request->item_num . '%')->get();
        // Redirect to the Process screen.
        // if ($request->item_num && ($po_rel->count() == 1)) {
        //     $poitem = $po_rel->first();
        //     $request->po_rel = $poitem->po_rel;
        //     $request->po_line = $poitem->po_line;
        //     return ($this->showPoProcess($request));
        // }
        $po_rel = $po_rel->first();
       // dd($po_rel,$request);
        return view('shipping.poreturn.itemlist')
            ->with('po_line', "") // Put empty line to show all PO Line item
            ->with('po_num', $request->po_num)
            ->with('whse', $request->whse_num)
            ->with('vend_num', $po_rel->vend_num)
            ->with('item_num', $request->item_num)
            ->with('unit_quantity_format', $unit_quantity_format);
    }

    public function showPoProcess(Request $request)
    {
        //dd($request->all());
        // dd('isnsn');
        if (!\Gate::allows('hasPoReturn')) {
            return view('errors.404')->with('page', 'error');;
        }
        $disable_lot_number_selection = 0;
        $defaults = [
            'vend_lot' => null,
            'lot_num' => null,
            'loc_num' => null,
            'qty_available' => null,
            'qty_available_conv' => null,
            'base_uom' => null,
            'uom_conv' => null,
        ];

        $tparm = new TparmView();
        $allow_over_return = $tparm->getTparmValue('POReturn', 'allow_over_return');
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $batch_id = generateBatchId("POReturn");

        $po_item = PurchaseOrderItem::with('item')
            ->where('po_num', $request->po_num)
            ->where('po_rel', $request->po_rel)
            ->where('po_line', $request->po_line)
            ->first();

        //$ItemList['item_desc'] = $po_item->item_desc;  // $request->item_desc;
        $item_num = $request->item ?? $request->item_num;
        //dd($item_num,$request);
        if ($item_num != "NON-INV") {
            //dd('a');
            if ($po_item) {
                $defaults = $this->getDefaultIssueLoc($po_item->whse_num, $po_item->item_num, $po_item->item->lot_tracked);


                if ($po_item->item->lot_tracked == 1) {
                    // Get tparm's disable_lot_number_selection value
                    $disable_lot_number_selection = $tparm->getTparmValue('POReturn', 'disable_lot_number_selection');

                    $lotloc = $this->getDefaultLocLotQtyELNS($po_item, $po_item->item, $disable_lot_number_selection);
                    if ($lotloc) {
                        $defaults['vend_lot'] = $lotloc->lot->vend_lot ?? null;
                        $defaults['lot_num'] = $lotloc->lot_num;
                        $defaults['loc_num'] = $lotloc->loc_num;
                        $defaults['uom_conv'] = $lotloc->uom;
                        $defaults['qty_available_conv'] = $lotloc->qty_available;
                        $defaults['qty_available'] = $lotloc->qty_available;
                        $def_loc = $lotloc->loc_num;
                    } else {
                        $defaults['vend_lot'] = null;
                        $defaults['lot_num'] = null;
                        $defaults['loc_num'] = null;
                        $defaults['uom_conv'] = null;
                        $defaults['qty_available_conv'] = null;
                        $defaults['qty_available'] = null;
                        $def_loc = "";
                    }
                }

                if ($po_item->item->uom != $po_item->uom && $defaults['qty_available']) {
                    $conv = UomConv::convert($po_item->item->uom, $defaults['qty_available'], $po_item->item->item_num, '', $po_item->vend_num, $po_item->uom);
                    $defaults['uom_conv'] = $conv['uom'];
                    $defaults['qty_available_conv'] = $conv['qty'];
                }
            }
            $non_inv = 0;
        } else {
            //dd('b');
            $non_inv = 1;
            $defaults['vend_lot'] = null;
            $defaults['lot_num'] = null;
            $defaults['loc_num'] = null;
            $defaults['uom_conv'] = null;
            $defaults['qty_available_conv'] = 0;
            $defaults['qty_available'] = null;
            $defaults['base_uom'] = $po_item->uom;
            $def_loc = "";
            //
        }

        $vendor_do = PurchaseOrder::select('vend_do')
            ->where('po_num', $request->po_num)
            ->where('vend_do', '!=', null)
            ->where('site_id', auth()->user()->site_id)
            ->value('vend_do');


        $input['ref_num'] = $request->po_num;
        $input['whse_num'] = $request->whse_num;
        $input['po_line'] = $request->po_line;
        $input['po_rel'] = $request->po_rel;
        $input['item_num'] = $item_num;
        $input['uom'] = $request->uom;
        $input['qty_returnable'] = $request->qty_returnable;
        $input['item_desc'] = $request->item_desc;
        $input['vend_do'] = session('vend_do');
        $input['indicate'] = 1;
        // return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
        $url = generateRedirectUrl('POReturn', $input);
        // dd($url->getTargetUrl());

        $view =  $po_item->item->catch_weight ? 'shipping.poreturn.process_cw' :'shipping.poreturn.process';

        return view($view)
            ->with('po_item', $po_item)
            ->with('ItemList', $po_item)
            ->with('vend_do', session('vend_do'))
            ->with('allow_over_return', $allow_over_return)
            ->with('defaults', $defaults)
            ->with('disable_lot_number_selection', $disable_lot_number_selection)
            ->with('disable_create_new_item_location', '0')
            ->with('sap_trans_order_integration', $sap_trans_order_integration)
            ->with('vendor_do', $vendor_do)
            ->with('url', $url->getTargetUrl())
            ->with('non_inv', $non_inv)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('def_loc', $def_loc)
            ->with('batch_id', $batch_id)
            ->with('printLabel', 0);
    }

    public function runPoProcess(POReturnRequest $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        $request = validateSansentiveValue($request);

        //DB::beginTransaction();
        //try {
        $po_rel = new PurchaseOrderItem();
        $polist = $po_rel->where('po_num', $request->ref_num)->where('po_rel', $request->ref_release)->where('po_line', $request->ref_line)->first();

        // Verifying POLine exist
        if (!$polist) {
            throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->ref_num . '-' . $request->ref_line . '-' . $request->ref_release . ']'])]);
        }

        // Verify PO status
        if ($polist->rel_status == 'C') {
            throw ValidationException::withMessages([__('error.mobile.status_is_completed', ['resource' => __('admin.label.po') . '-' . $request->ref_num])]);
        }




        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_require_check_online == 1 && $sap_trans_order_integration == 1) {
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id, null, 'PO Return', 1);

            if ($checkConnection > 2) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }
        }





        if ($request->item_num == "NON-INV") {
            $this->validate(
                $request,
                [
                    'ref_num' => 'required',
                    'ref_line' => 'required',
                    'ref_release' => 'required',
                    //  'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
                    'qty' => 'required',
                    'reason_code' => 'required|string|max:10|exists:reason_codes,reason_num,site_id,' . auth()->user()->site_id . ',reason_class,POReturn'
                ]
            );
        } else {
            $this->validate(
                $request,
                [
                    'ref_num' => 'required',
                    'ref_line' => 'required',
                    'ref_release' => 'required',
                    'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
                    'qty' => 'required',
                    'reason_code' => 'required|string|max:10|exists:reason_codes,reason_num,site_id,' . auth()->user()->site_id . ',reason_class,POReturn'
                ],
                [
                    'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
                ]
            );
        }
        //            if ($request->reason_code != "SAPCN") {
        //                DB::table('sap_po_batch_sync')->insert([
        //                    'po_num' => $request->ref_num,
        //                    'po_line' => $request->ref_line,
        //                    'po_rel' => $request->ref_release ?? 0,
        //                    'reason_code' => $request->reason_code,
        //                    'is_return' => 1,
        //                    'doc_num' => $request->document_num,
        //                    'erp_ID' => po_item::select('erp_ID')->where('po_num', $request->ref_num)
        //                            ->where('po_line', $request->ref_line)
        //                            //->where('po_rel', $po_rel)
        //                            ->where('site_id', auth()->user()->site_id)
        //                            ->value('erp_ID'),
        //                    'whse_num' => $request->whse_num,
        //                    'po_type' => 2,
        //                    'loc_num' => $request->loc_num,
        //                    'lot_num' => $request->lot_num,
        //                    'expiry_date' => $request->expiry_date,
        //                    'item_num' => $request->item_num,
        //                    'vend_do' => $request->vend_do,
        //                    'vend_num' => $request->vend_num,
        //                    'qty_received' => null,
        //                    'qty_required' => null,
        //                    'qty_returned' => $request->qty,
        //                    'qty_returned_uom' => $request->uom,
        //                    'sap_base_entry' => $request->sap_base_entry,
        //                    'sap_base_line' => $request->sap_base_line,
        //                    'sync_status' => 1,
        //                    'site_id' => auth()->user()->site_id,
        //                    'created_by' => auth()->user()->name,
        //                    'modified_by' => auth()->user()->name,
        //                    'created_date' => now(),
        //                    'modified_date' => now(),
        //                ]);
        //            }
        //dd('mari',$request,'sini');
        PoService::executePOReturn($request);
        Alert::success('Success', __('success.processed', ['process' => __('Purchase Order Return')]));
        // dd($request);
        /*             * *********Start SAP ******************* */
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
        // SAP Intergration

        $syncresult = ReasonCode::where('reason_num', $request->reason_code)->where('reason_class', $request->reason_class)->first();
        if ($sap_trans_order_integration == 1 && $syncresult && $syncresult->sync_status == "Y") {
            if ($request->last_return == "Yes") {
                if (@$request->document_num != "" || @$request->document_num != null) {
                    DB::table('sap_po_batch_sync')->insert([
                        'po_num' => $request->ref_num,
                        'po_line' => $request->ref_line,
                        'po_rel' => $request->ref_release ?? 0,
                        'reason_code' => $request->reason_code,
                        'is_return' => 1,
                        'doc_num' => $request->document_num,
                        'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $request->ref_num)
                            ->where('po_line', $request->ref_line)
                            //->where('po_rel', $po_rel)
                            ->where('site_id', auth()->user()->site_id)
                            ->value('erp_ID'),
                        'whse_num' => $request->whse_num,
                        'po_type' => 2,
                        'loc_num' => $request->loc_num,
                        'lot_num' => $request->lot_num,
                        'expiry_date' => $request->expiry_date,
                        'item_num' => $request->item_num,
                        'vend_do' => $request->vend_do,
                        'vend_num' => $request->vend_num,
                        'qty_received' => null,
                        'qty_required' => null,
                        'qty_returned' => $request->qty,
                        'qty_returned_uom' => $request->uom,
                        'qty_conv' => $request->qty_conv,
                        'sap_base_entry' => $request->sap_base_entry,
                        'sap_base_line' => $request->sap_base_line,
                        'sync_status' => 1,
                        'site_id' => auth()->user()->site_id,
                        'created_by' => auth()->user()->name,
                        'modified_by' => auth()->user()->name,
                        'created_date' => now(),
                        'modified_date' => now(),
                    ]);
                }
                //$res = SapCallService::postPOReturn($request, null);
                if ($sap_single_bin == 1) {

                    $result = SiteConnectionService::postIntergrationTransPO("PO Return", $request, null);
                    // $result = SapCallService::postPOReturn($request, null);
                } else {
                    if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                        // Later Change to read from Matltrans
                        $result = SapCallService::postPOReturn($request, null);
                    } else {
                        $result = SapCallService::postPOReturn($request, null);
                    }
                }
                //dd($result);
                if ($result != 200) {
                    if ($result == 404) {
                        // Goods Return Request does not exist in SAP.
                        Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_goods_return_request_notexist'))->persistent('Dismiss');
                    } else {
                        Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                    }
                    return redirect()->route('backshowPoLineReturn', ['whse_num' => $request->whse_num, 'po_line' => $request->ref_line, 'po_num' => $request->ref_num, 'vend_do' => $request->vend_do]);
                }
            } else {

                DB::table('sap_po_batch_sync')->insert([
                    'po_num' => $request->ref_num,
                    'po_line' => $request->ref_line,
                    'po_rel' => $request->ref_release ?? 0,
                    'reason_code' => $request->reason_code,
                    'is_return' => 1,
                    'doc_num' => $request->document_num,
                    'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $request->ref_num)
                        ->where('po_line', $request->ref_line)
                        //->where('po_rel', $po_rel)
                        ->where('site_id', auth()->user()->site_id)
                        ->value('erp_ID'),
                    'whse_num' => $request->whse_num,
                    'po_type' => 2,
                    'loc_num' => $request->loc_num,
                    'lot_num' => $request->lot_num,
                    'expiry_date' => $request->expiry_date,
                    'item_num' => $request->item_num,
                    'vend_do' => $request->vend_do,
                    'vend_num' => $request->vend_num,
                    'qty_received' => null,
                    'qty_required' => null,
                    'qty_returned' => $request->qty,
                    'qty_conv' => $request->qty_conv,
                    'qty_returned_uom' => $request->uom,
                    'sap_base_entry' => $request->sap_base_entry,
                    'sap_base_line' => $request->sap_base_line,
                    'sync_status' => 1,
                    'site_id' => auth()->user()->site_id,
                    'created_by' => auth()->user()->name,
                    'modified_by' => auth()->user()->name,
                    'created_date' => now(),
                    'modified_date' => now(),
                ]);
            }
            //                $returnrequest_pending_list = DB::table('sap_po_batch_sync')
            //                        ->where('status', 'Pending')
            //                        ->where('vend_num', $request->vend_num)
            //                        ->where('doc_num', $request->document_num)
            //                        ->where('sync_status', "<", 3)
            //                        //->where('created_by', auth()->user()->name)
            //                        ->where('po_type', 2)
            //                        ->where('site_id', auth()->user()->site_id)
            //                        ->get();
            //                $arrProcess = $returnrequest_pending_list->toArray();
            //
            //                $arrBulkStore = array();
            //                foreach ($arrProcess as $key => $value) {
            //                    $uniqueKey = $value->vend_num . "-" . $value->po_num . "-" . $value->doc_num;
            //
            //                    $arrBulkStore[$uniqueKey][] = $arrProcess[$key];
            //
            ////            $arrBulkStore[$value->vend_num][] = $arrProcess[$key];
            //                }
            //                dd($arrBulkStore);
            //                 foreach ($arrBulkStore as $key => $arrProcess) {
            //
            ////                                    $res =SapCallService::postPOReturn($request);
            //
            //                    $result = SapCallService::postPOReturnBulk($request, $arrProcess);
            //                }
            //                dd($arrBulkStore,$request->input());
            //                $res =SapCallService::postPOReturn($request,null);
        }

        /*             * *********End SAP********************** */

        // Session::put('modulename', 'POReturn');
        // Session::put('po_num',$request->ref_num);


        //  DB::commit();
        // return app('App\Http\Controllers\RouteController')->gotoPOReturn();
        $input['ref_num'] = $request->ref_num;
        $input['whse_num'] = $request->whse_num;
        $input['po_line'] = $request->ref_line;
        $input['po_rel'] = $request->ref_release;
        $input['item_num'] = $request->item_num;
        $input['uom'] = $request->uom;
        $input['qty_returnable'] = $request->qty_returnable;
        $input['item_desc'] = $request->item_desc;
        $input['vend_do'] = $request->vend_do;
        $input['indicate'] = 0;
        // return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
        $url = generateRedirectUrl('POReturn', $input);
        // dd($url);
        return $url;

        // Empty
        //return redirect()->route('poReturn');



        // } catch (Exception $e) {
        //  DB::rollback();
        //  throw $e;
        // }
    }

    public function runPoCWProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        $request = validateSansentiveValue($request);

        $validateErrors = self::poReturnValidation($request);

        if (count($validateErrors[0]) > 0 ?? []) {
            $errors = $validateErrors[0];
            return back()->withErrors($errors)->withInput();
        }

        // move to validation function
        // $po_rel = new PurchaseOrderItem();
        // $polist = $po_rel->where('po_num', $request->ref_num)->where('po_rel', $request->ref_release)->where('po_line', $request->ref_line)->first();

        // // Verifying POLine exist
        // if (!$polist) {
        //     throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->ref_num . '-' . $request->ref_line . '-' . $request->ref_release . ']'])]);
        // }

        // // Verify PO status
        // if ($polist->rel_status == 'C') {
        //     throw ValidationException::withMessages([__('error.mobile.status_is_completed', ['resource' => __('admin.label.po') . '-' . $request->ref_num])]);
        // }




        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_require_check_online == 1 && $sap_trans_order_integration == 1) {
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id, null, 'PO Return', 1);

            if ($checkConnection > 2) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }
        }

        $siteSettings = new SiteSetting();
        $this->timezone = $siteSettings->getTimezone();
        $now =  Timezone::convertFromUTC(now(), $this->timezone, SiteSetting::getOutputDateFormat() .' H:i:s');

        DB::beginTransaction();
        try {
            PoService::executePOReturn($request);
            Alert::success('Success', __('success.processed', ['process' => __('Purchase Order Return')]));
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
        // dd($request);
        /*             * *********Start SAP ******************* */
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
        // SAP Intergration

        $syncresult = ReasonCode::where('reason_num', $request->reason_code)->where('reason_class', $request->reason_class)->first();
        if ($sap_trans_order_integration == 1 && $syncresult && $syncresult->sync_status == "Y") {
            if ($request->last_return == "Yes") {
                if (@$request->document_num != "" || @$request->document_num != null) {
                    DB::table('sap_po_batch_sync')->insert([
                        'po_num' => $request->ref_num,
                        'po_line' => $request->ref_line,
                        'po_rel' => $request->ref_release ?? 0,
                        'reason_code' => $request->reason_code,
                        'is_return' => 1,
                        'doc_num' => $request->document_num,
                        'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $request->ref_num)
                            ->where('po_line', $request->ref_line)
                            //->where('po_rel', $po_rel)
                            ->where('site_id', auth()->user()->site_id)
                            ->value('erp_ID'),
                        'whse_num' => $request->whse_num,
                        'po_type' => 2,
                        'loc_num' => $request->loc_num,
                        'lot_num' => $request->lot_num,
                        'expiry_date' => $request->expiry_date,
                        'item_num' => $request->item_num,
                        'vend_do' => $request->vend_do,
                        'vend_num' => $request->vend_num,
                        'qty_received' => null,
                        'qty_required' => null,
                        'qty_returned' => $request->qty,
                        'qty_returned_uom' => $request->uom,
                        'qty_conv' => $request->qty_conv,
                        'sap_base_entry' => $request->sap_base_entry,
                        'sap_base_line' => $request->sap_base_line,
                        'sync_status' => 1,
                        'site_id' => auth()->user()->site_id,
                        'created_by' => auth()->user()->name,
                        'modified_by' => auth()->user()->name,
                        'created_date' => now(),
                        'modified_date' => now(),
                    ]);
                }
                //$res = SapCallService::postPOReturn($request, null);
                if ($sap_single_bin == 1) {

                    $result = SiteConnectionService::postIntergrationTransPO("PO Return", $request, null);
                    // $result = SapCallService::postPOReturn($request, null);
                } else {
                    if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                        // Later Change to read from Matltrans
                        $result = SapCallService::postPOReturn($request, null);
                    } else {
                        $result = SapCallService::postPOReturn($request, null);
                    }
                }
                //dd($result);
                if ($result != 200) {
                    if ($result == 404) {
                        // Goods Return Request does not exist in SAP.
                        Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_goods_return_request_notexist'))->persistent('Dismiss');
                    } else {
                        Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                    }
                    return redirect()->route('backshowPoLineReturn', ['whse_num' => $request->whse_num, 'po_line' => $request->ref_line, 'po_num' => $request->ref_num, 'vend_do' => $request->vend_do]);
                }
            } else {

                DB::table('sap_po_batch_sync')->insert([
                    'po_num' => $request->ref_num,
                    'po_line' => $request->ref_line,
                    'po_rel' => $request->ref_release ?? 0,
                    'reason_code' => $request->reason_code,
                    'is_return' => 1,
                    'doc_num' => $request->document_num,
                    'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $request->ref_num)
                        ->where('po_line', $request->ref_line)
                        //->where('po_rel', $po_rel)
                        ->where('site_id', auth()->user()->site_id)
                        ->value('erp_ID'),
                    'whse_num' => $request->whse_num,
                    'po_type' => 2,
                    'loc_num' => $request->loc_num,
                    'lot_num' => $request->lot_num,
                    'expiry_date' => $request->expiry_date,
                    'item_num' => $request->item_num,
                    'vend_do' => $request->vend_do,
                    'vend_num' => $request->vend_num,
                    'qty_received' => null,
                    'qty_required' => null,
                    'qty_returned' => $request->qty,
                    'qty_conv' => $request->qty_conv,
                    'qty_returned_uom' => $request->uom,
                    'sap_base_entry' => $request->sap_base_entry,
                    'sap_base_line' => $request->sap_base_line,
                    'sync_status' => 1,
                    'site_id' => auth()->user()->site_id,
                    'created_by' => auth()->user()->name,
                    'modified_by' => auth()->user()->name,
                    'created_date' => now(),
                    'modified_date' => now(),
                ]);
            }
            //                $returnrequest_pending_list = DB::table('sap_po_batch_sync')
            //                        ->where('status', 'Pending')
            //                        ->where('vend_num', $request->vend_num)
            //                        ->where('doc_num', $request->document_num)
            //                        ->where('sync_status', "<", 3)
            //                        //->where('created_by', auth()->user()->name)
            //                        ->where('po_type', 2)
            //                        ->where('site_id', auth()->user()->site_id)
            //                        ->get();
            //                $arrProcess = $returnrequest_pending_list->toArray();
            //
            //                $arrBulkStore = array();
            //                foreach ($arrProcess as $key => $value) {
            //                    $uniqueKey = $value->vend_num . "-" . $value->po_num . "-" . $value->doc_num;
            //
            //                    $arrBulkStore[$uniqueKey][] = $arrProcess[$key];
            //
            ////            $arrBulkStore[$value->vend_num][] = $arrProcess[$key];
            //                }
            //                dd($arrBulkStore);
            //                 foreach ($arrBulkStore as $key => $arrProcess) {
            //
            ////                                    $res =SapCallService::postPOReturn($request);
            //
            //                    $result = SapCallService::postPOReturnBulk($request, $arrProcess);
            //                }
            //                dd($arrBulkStore,$request->input());
            //                $res =SapCallService::postPOReturn($request,null);
        }

        /*             * *********End SAP********************** */

        $input['ref_num'] = $request->ref_num;
        $input['whse_num'] = $request->whse_num;
        $input['po_line'] = $request->ref_line;
        $input['po_rel'] = $request->ref_release;
        $input['item_num'] = $request->item_num;
        $input['uom'] = $request->uom;
        $input['qty_returnable'] = $request->qty_returnable;
        $input['item_desc'] = $request->item_desc;
        $input['vend_do'] = $request->vend_do;
        $input['indicate'] = 0;
        // return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
        $url = generateRedirectUrl('POReturn', $input);
        return $url;
    }

    public function POItemList(Request $request)
    {

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($request->ajax()) {
            $output = '';
            $item_num = $request->get('item_num');
            $po_num = $request->get('po_num');
            $po_line = $request->get('po_line');
            $whse_num = $request->get('whse_num');
            $search = $request->get('query');

            if ($search != '') {
                $po_item = new PurchaseOrderItem();

                $data = PurchaseOrderItem::where('po_num', $po_num)
                    ->where('whse_num', $whse_num);

                if ($item_num && $item_num != 'NON-INV') {
                    $data = $data->where('item_num', $item_num);
                }

                if ($po_line != "") {
                    $data = $data->where('po_line', $po_line);
                }


                $data = $data->where('item_num', $item_num)
                    ->where('rel_status', '!=', 'C')
                    ->where('item_num', '!=', 'NON-INV')
                    ->where('qty_received', '>', 0)
                    ->where(function ($q) use ($search) {
                        $q->orwhere('po_line', 'like', '%' . $search . '%')
                            ->orWhere('po_rel', 'like', '%' . $search . '%')
                            ->orWhere('item_num', 'like', '%' . $search . '%')
                            ->orWhere('item_desc', 'like', '%' . $search . '%')
                            ->orWhere('vend_do', 'like', '%' . $search . '%')
                            ->orderBy('po_num');
                    })->orderByRaw('cast(po_line as unsigned) ASC')
                    ->get();
            } else {
                $data = PurchaseOrderItem::where('po_num', $po_num)
                    ->orderBy('po_num')
                    ->where('whse_num', $whse_num);

                if ($item_num  && $item_num != 'NON-INV') {
                    $data = $data->where('item_num', $item_num);
                }
                if ($po_line != "") {
                    $data = $data->where('po_line', $po_line);
                }

                $data = $data->where('qty_received', '>', 0)
                    ->where('rel_status', '!=', 'C')
                    ->orderByRaw('cast(po_line as unsigned) ASC')
                    ->get();
            }
            //dd($data);
            // Filter out
            $data = $data->filter(function ($data) {
                return $data->qty_returnable > 0;
            });
            $total_row = $data->count();
            // dd($total_row);
            if ($total_row > 0) {
                foreach ($data as $row) {
                    $altBarCode = AlternateBarcode::where('item_num', $row->item_num)->where('site_id', auth()->user()->site_id)->get();
                    //  if($row->qty_returnable > 0 ) {
                    $output .= '
                    <form class="form form-horizontal" method="get" action="/home/<USER>/po-return/process">
                        <div class="row border border-primary" id="mybox" onclick="javascript:this.parentNode.submit();">
                            <div class="col-xs-12">
                                <table width="100%">
                                    <input type="hidden" name="_token" value="' . csrf_token() . '">
                                    <tr>
                                        <td>
                                            <label for="po_line">' . __('mobile.label.po_line') . '</label>
                                        </td>
                                        <td>
                                         <input value="' . $row->whse_num . '" name="whse_num" type="hidden">
                                          <input value="' . $row->vend_do . '" name="vend_do" type="hidden">
                                           <input value="' . $row->vend_num . '" name="vend_num" type="hidden">
                                            <input value="' . $row->vend_name . '" name="vend_name" type="hidden">
                                            <input value="' . $row->po_num . '" name="po_num" type="hidden">
                                            <input value="' . $row->po_line . '" name="po_line" type="hidden">
                                            <input value="' . $row->po_rel . '" name="po_rel" type="hidden">
                                             <input value="' . $row->item_num . '" name="item_num" type="hidden">
                                            <span class="form-control border-primary pseudoinput">' . $row->po_line . ' </span>
                                            <input  size="3" type="hidden" id="po_line" class="form-control border-primary" value="' . $row->po_line . '" readonly>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td> <label for="item_num">' . __('mobile.label.item_num') . ' </label></td>
                                        <td colspan="4">';
                    foreach ($altBarCode as $barCode) {
                        $output .= '<span style="display: none"> ' . @$barCode->alternate_barcode . ' </span>';
                    }
                    $output .= '
                                            <span class="form-control border-primary pseudoinput">' . $row->item_num . ' </span>
                                            <input size="10" style="text-align:left;" type="hidden" id="item" class="form-control border-primary" name="item" value="' . $row->item_num . '" readonly>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td colspan="4">
                                            <textarea readonly type="text" style="text-align:left;font-size:15px;" class="form-control border-primary">' . $row->item_desc . '</textarea>
                                            <input size="10" style="text-align:left;" type="hidden" id="description" name="item_desc" class="inputtext form-control border-primary"  value="' . $row->item_desc . '" readonly>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="90px">
                                            <label for="qty_returnable">  ' . __('mobile.label.qty_returnable') . '</label>
                                        </td>
                                        <td width="40%">
                                            <span class="form-control border-primary pseudoinput" style="text-align: right;">' . numberFormatPrecision($row->qty_returnable, $unit_quantity_format, '.', '') . ' </span>
                                            <input size="10" type="hidden" id="qty_returnable" class="form-control border-primary" name="qty_returnable" style="text-align: right;" value="' . numberFormatPrecision($row->qty_returnable, $unit_quantity_format, '.', '') . '" readonly>
                                        </td>
                                        <td>
                                            <span class="form-control border-primary pseudoinput">' . $row->uom . ' </span>
                                            <input type="hidden" id="uom" name="uom" class="form-control border-primary" value="' . $row->uom . '" readonly>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </form>
                    ';
                    //   }
                }
            } else {
                $output = "
                <tr>
                    <td align='center' colspan='4'>Record not found</td>
                </tr>
                ";
            }
            $data = array(
                'table_data' => $output,
                'total_data' => $total_row
            );

            echo json_encode($data);
        }
    }

    // -----------------
    // GRN
    // -----------------
    public function showGrnLine(Request $request)
    {
        Session::put('request_data_poreturn', $request->all());
        Session::put('return_type', $request->return_type);

        // dd($request->all());
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($request->item_num != "NON-INV") {
            $request->validate([
                'item_num' => 'nullable|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
                'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            ], [
                'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
                'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            ]);
        }

        $checkGrn = GRN::where('grn_num', $request->grn_num)->where('whse_num', $request->whse_num)->where('grn_status', '!=', "C")->exists();
        // Send error if grn_num's status is not open
        if (!$checkGrn) {
            // throw ValidationException::withMessages(['grn_num' => 'GRN-' . $request->grn_num . ' cannot be proceed due to status is completed/closed']);
            throw ValidationException::withMessages(['grn_num' => __('error.mobile.resource_completed', ['resource' => __('mobile.label.grn')])]);
        }

        $checkGrn = GRN::where('grn_num', $request->grn_num)->where('whse_num', $request->whse_num)->first();
        // Send error if grn not found
        if (!$checkGrn) {
            throw ValidationException::withMessages(['grn_num' => 'GRN [' . $request->grn_num .  '] does not match Whse [' . $request->whse_num . ']']);
        }

        $vend_num = $checkGrn->vend_num;
        Session::put('vend_do', $request->vend_do);
        $item = new Item();
        $po_rel = new PurchaseOrderItem();

        if ($request->item_num == null) {
            $getGrn = GRNItem::where('grn_num', $request->grn_num)->where('net_received', '>', '0')->get();
        } else {
            $getGrn = GRNItem::where('grn_num', $request->grn_num)->where('item_num', $request->item_num)->where('net_received', '>', '0')->get();
        }

        if ($getGrn->count() == 0) {
            Alert::error('Error', __('error.mobile.norecord'));
            throw ValidationException::withMessages([]);
        }

        $request['indicate_single'] = 0;
        // Redirect to the Process screen.
        if ($getGrn->count() == 1) {
            $grnitem = $getGrn->first();
            $request['grn_line'] = $grnitem->grn_line;
            $request['item_num'] =  $grnitem->item_num;

            return redirect()->route('showGrnReturnProcess', $request->all(), 303);
        }
        // $grnitem = $getGrn->first();

        return view('shipping.poreturn.grnitemlist')
            ->with('grn_num', $request->grn_num)
            ->with('whse', $request->whse_num)
            ->with('vend_num', $vend_num)
            ->with('item_num', $request->item_num)
            ->with('unit_quantity_format', $unit_quantity_format);
    }

    public function showGrnProcess(Request $request)
    {
        //dd($request->all());
        // dd('isnsn');
        if (!\Gate::allows('hasPoReturn')) {
            return view('errors.404')->with('page', 'error');
        }
        $disable_lot_number_selection = 0;
        $defaults = [
            'vend_lot' => null,
            'lot_num' => null,
            'loc_num' => null,
            'qty_available' => null,
            'qty_available_conv' => null,
            'base_uom' => null,
            'uom_conv' => null,
        ];

        $tparm = new TparmView();
        $allow_over_return = $tparm->getTparmValue('POReturn', 'allow_over_return');
        $sap_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $batch_id = generateBatchId("POReturn");
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');

        $grn_item = GRNItem::with('grn')->with('item')
            ->where('grn_num', $request->grn_num)
            ->where('grn_line', $request->grn_line)
            ->first();

        $grn_item['qty_returnable'] = $grn_item->net_received;
        $grn_item['whse_num'] = $grn_item->grn->whse_num;
        //$ItemList['item_desc'] = $po_item->item_desc;  // $request->item_desc;
        $item_num = $request->item ?? $request->item_num;
        //dd($item_num,$request);
        if ($item_num != "NON-INV") {
            //dd('a');
            if ($grn_item) {
                $defaults = $this->getDefaultIssueLoc($grn_item->whse_num, $grn_item->item_num, $grn_item->item->lot_tracked);


                if ($grn_item->item->lot_tracked == 1) {
                    // Get tparm's disable_lot_number_selection value
                    $disable_lot_number_selection = $tparm->getTparmValue('POReturn', 'disable_lot_number_selection');

                    $lotloc = $this->getDefaultLocLotQtyELNS($grn_item, $grn_item->item, $disable_lot_number_selection);
                    if ($lotloc) {
                        $defaults['vend_lot'] = $lotloc->lot->vend_lot ?? null;
                        $defaults['lot_num'] = $lotloc->lot_num;
                        $defaults['loc_num'] = $lotloc->loc_num;
                        $defaults['uom_conv'] = $lotloc->uom;
                        $defaults['qty_available_conv'] = $lotloc->qty_available;
                        $defaults['qty_available'] = $lotloc->qty_available;
                    } else {
                        $defaults['vend_lot'] = null;
                        $defaults['lot_num'] = null;
                        $defaults['loc_num'] = null;
                        $defaults['uom_conv'] = null;
                        $defaults['qty_available_conv'] = null;
                        $defaults['qty_available'] = null;
                    }
                }

                if ($grn_item->item->uom != $grn_item->uom && $defaults['qty_available']) {
                    $conv = UomConv::convert($grn_item->item->uom, $defaults['qty_available'], $grn_item->item->item_num, '', $grn_item->vend_num, $grn_item->uom);
                    $defaults['uom_conv'] = $conv['uom'];
                    $defaults['qty_available_conv'] = $conv['qty'];
                }
            }
            $non_inv = 0;
        } else {
            //dd('b');
            $non_inv = 1;
            $defaults['vend_lot'] = null;
            $defaults['lot_num'] = null;
            $defaults['loc_num'] = null;
            $defaults['uom_conv'] = null;
            $defaults['qty_available_conv'] = 0;
            $defaults['qty_available'] = null;
            $defaults['base_uom'] = $grn_item->uom;
            //
        }

        $vendor_do = $grn_item->grn->vend_do;


        // Got Select Item
        if ($request->indicate_single == 1) {
            $input['grn_num'] = $request->grn_num;
            $input['whse_num'] =   $grn_item['whse_num'];
            $input['item_num'] = $request->item_num;
            $input['vend_name'] = $request->vend_name;
            $input['return_type'] = 'GRN';
            $input['vend_do'] = session('vend_do');
            $input['indicate'] = 2;
        } else {
            // No Select Item
            $input['grn_num'] = $request->grn_num;
            $input['whse_num'] =   $grn_item['whse_num'];
            $input['item_num'] = null;
            $input['vend_name'] = $request->vend_name;
            $input['return_type'] = 'GRN';
            $input['vend_do'] = session('vend_do');
            $input['indicate'] = 1;
            // return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);

        }
        $url = generateRedirectUrl('GRNReturn', $input);



        return view('shipping.poreturn.grnprocess')
            ->with('grn_item', $grn_item)
            ->with('vend_do', session('vend_do'))
            ->with('allow_over_return', $allow_over_return)
            ->with('defaults', $defaults)
            ->with('disable_lot_number_selection', $disable_lot_number_selection)
            ->with('sap_integration', $sap_integration)
            ->with('vendor_do', $vendor_do)
            ->with('url', $url->getTargetUrl()) //$url->getTargetUrl())
            ->with('non_inv', $non_inv)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('batch_id', $batch_id)
            ->with('total_quantity_format', $total_quantity_format);
    }
    public function GRNItemList(Request $request)
    {

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($request->ajax()) {
            $output = '';
            $item_num = $request->get('item_num');
            $grn_num = $request->get('grn_num');
            $whse_num = $request->get('whse_num');
            $search = $request->get('query');

            if ($search != '') {
                $po_item = new GRNItem();

                $data = GRNItem::where('grn_num', $grn_num);
                // ->where('whse_num', $whse_num);

                if ($item_num && $item_num != 'NON-INV') {
                    $data = $data->where('item_num', $item_num);
                }

                $data = $data->where('item_num', $item_num)
                    ->where('status', '!=', 'C')
                    ->where('item_num', '!=', 'NON-INV')
                    ->where('net_received', '>', 0)
                    ->where(function ($q) use ($search) {
                        $q->orwhere('grn_line', 'like', '%' . $search . '%')
                            ->orWhere('item_num', 'like', '%' . $search . '%')
                            ->orWhere('item_desc', 'like', '%' . $search . '%')
                            ->orWhere('vend_do', 'like', '%' . $search . '%')
                            ->orderBy('grn_num');
                    })->orderByRaw('cast(grn_line as unsigned) ASC')
                    ->get();
            } else {
                $data = GRNItem::where('grn_num', $grn_num)
                    ->orderBy('grn_num');
                // ->where('whse_num', $whse_num);

                if ($item_num  && $item_num != 'NON-INV') {
                    $data = $data->where('item_num', $item_num);
                }

                $data = $data->where('net_received', '>', 0)
                    ->where('status', '!=', 'C')
                    ->orderByRaw('cast(grn_line as unsigned) ASC')
                    ->get();
            }
            //dd($data);
            // Filter out
            $data = $data->filter(function ($data) {
                return $data->net_received > 0;
            });
            $total_row = $data->count();
            // dd($total_row);
            if ($total_row > 0) {
                foreach ($data as $row) {
                    $po_num = $row->po_num . '-' . $row->po_line;
                    $altBarCode = AlternateBarcode::where('item_num', $row->item_num)->where('site_id', auth()->user()->site_id)->get();
                    //  if($row->qty_returnable > 0 ) {
                    $output .= '
                    <form class="form form-horizontal" method="get" action="/home/<USER>/grn-return/process">
                        <div class="row border border-primary" id="mybox" onclick="javascript:this.parentNode.submit();">
                            <div class="col-xs-12">
                                <table width="100%">
                                    <input type="hidden" name="_token" value="' . csrf_token() . '">
                                    <tr>
                                        <td>
                                            <label for="grn_line">' . __('mobile.label.grn_line') . '</label>
                                        </td>
                                        <td>
                                          <input value="' . $row->vend_do . '" name="vend_do" type="hidden">
                                           <input value="' . $row->vend_num . '" name="vend_num" type="hidden">
                                            <input value="' . $row->vend_name . '" name="vend_name" type="hidden">
                                            <input value="' . $row->grn_num . '" name="grn_num" type="hidden">
                                            <input value="' . $row->grn_line . '" name="grn_line" type="hidden">
                                             <input value="' . $row->item_num . '" name="item_num" type="hidden">
                                            <span class="form-control border-primary pseudoinput">' . $row->grn_line . ' </span>
                                            <input  size="3" type="hidden" id="grn_line" class="form-control border-primary" value="' . $row->grn_line . '" readonly>
                                        </td>
                                        <td>
                                            <label for="po_num">' . __('mobile.label.po_num') . '</label>
                                        </td>
                                        <td>
                                            <span class="form-control border-primary pseudoinput">' . $row->po_num . '-' . $row->po_line . ' </span>
                                            <input  size="3" type="hidden" id="po_num" class="form-control border-primary" value="' . $row->po_num . '" readonly>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td> <label for="item_num">' . __('mobile.label.item_num') . ' </label></td>
                                        <td colspan="4">';
                    foreach ($altBarCode as $barCode) {
                        $output .= '<span style="display: none"> ' . @$barCode->alternate_barcode . ' </span>';
                    }
                    $output .= '
                                            <span class="form-control border-primary pseudoinput">' . $row->item_num . ' </span>
                                            <input size="10" style="text-align:left;" type="hidden" id="item" class="form-control border-primary" name="item" value="' . $row->item_num . '" readonly>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td colspan="4">
                                            <textarea readonly type="text" style="text-align:left;font-size:15px;" class="form-control border-primary">' . $row->item_desc . '</textarea>
                                            <input size="10" style="text-align:left;" type="hidden" id="description" name="item_desc" class="inputtext form-control border-primary"  value="' . $row->item_desc . '" readonly>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="90px">
                                            <label for="qty_returnable">  ' . __('mobile.label.qty_returnable') . '</label>
                                        </td>
                                        <td width="40%">
                                            <span class="form-control border-primary pseudoinput" style="text-align: right;">' . numberFormatPrecision($row->net_received, $unit_quantity_format, '.', '') . ' </span>
                                            <input size="10" type="hidden" id="qty_returnable" class="form-control border-primary" name="qty_returnable" style="text-align: right;" value="' . numberFormatPrecision($row->net_received, $unit_quantity_format, '.', '') . '" readonly>
                                        </td>
                                        <td colspan="2">
                                            <span class="form-control border-primary pseudoinput">' . $row->uom . ' </span>
                                            <input type="hidden" id="uom" name="uom" class="form-control border-primary" value="' . $row->uom . '" readonly>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </form>
                    ';
                    //   }
                }
            } else {
                $output = "
                <tr>
                    <td align='center' colspan='4'>Record not found</td>
                </tr>
                ";
            }
            $data = array(
                'table_data' => $output,
                'total_data' => $total_row
            );

            echo json_encode($data);
        }
    }
    public function runGrnProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        $errors = [];
        $request = validateSansentiveValue($request);
        $validateErrors = self::poReturnValidation($request);

        if (count($validateErrors[0]) > 0 ?? []) {
            $errors = $validateErrors[0];
            return back()->withErrors($errors)->withInput();
        }

        DB::beginTransaction();
        try {

            // $grnitem = GRNItem::where('grn_num', $request->grn_num)->where('grn_line', $request->grn_line)->first();

            // // Verifying POLine exist
            // if (!$grnitem) {
            //     throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->grn_num . '-' . $request->grn_line . ']'])]);
            // }

            // // Verify PO status
            // if ($grnitem->status == 'C') {
            //     // throw ValidationException::withMessages([__('error.mobile.status_is_completed', ['resource' => __('admin.label.grn') . '-' . $request->grn_num])]);
            //     throw ValidationException::withMessages([__('error.mobile.resource_completed', ['resource' => __('mobile.label.grn')])]);
            // }

            if ($request->item_num == "NON-INV") {
                $this->validate(
                    $request,
                    [
                        'grn_num' => 'required',
                        'grn_line' => 'required',
                        //  'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
                        'qty' => 'required',
                        'reason_code' => 'required|string|max:10|exists:reason_codes,reason_num,site_id,' . auth()->user()->site_id . ',reason_class,POReturn'
                    ]
                );
            } else {
                $this->validate(
                    $request,
                    [
                        'grn_num' => 'required',
                        'grn_line' => 'required',
                        'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
                        'qty' => 'required',
                        'reason_code' => 'required|string|max:10|exists:reason_codes,reason_num,site_id,' . auth()->user()->site_id . ',reason_class,POReturn'
                    ],
                    [
                        'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
                    ]
                );
            }

            $po_rel = PurchaseOrderItem::where('po_num', $request->po_num)->where('po_line', $request->po_line)->pluck('po_rel')->first();

            $request->merge([
                'ref_num' => $request->po_num,
                'ref_line' => $request->po_line,
                'ref_release' => $po_rel
            ]);

            PoService::executeGRNReturn($request);
            PoService::executePOReturn($request);
            Alert::success('Success', __('success.processed', ['process' => __('GRN Return')]));
            /*             * *********Start SAP ******************* */
            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
            // SAP Intergration

            $syncresult = ReasonCode::where('reason_num', $request->reason_code)->where('reason_class', $request->reason_class)->first();
            if ($sap_trans_order_integration == 1 && $syncresult && $syncresult->sync_status == "Y") {
                if ($request->last_return == "Yes") {
                    if (@$request->document_num != "" || @$request->document_num != null) {
                        DB::table('sap_po_batch_sync')->insert([
                            'po_num' => $request->ref_num,
                            'po_line' => $request->ref_line,
                            'po_rel' => $request->ref_release ?? 0,
                            'reason_code' => $request->reason_code,
                            'is_return' => 1,
                            'doc_num' => $request->document_num,
                            'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $request->ref_num)
                                ->where('po_line', $request->ref_line)
                                //->where('po_rel', $po_rel)
                                ->where('site_id', auth()->user()->site_id)
                                ->value('erp_ID'),
                            'whse_num' => $request->whse_num,
                            'po_type' => 2,
                            'loc_num' => $request->loc_num,
                            'lot_num' => $request->lot_num,
                            'expiry_date' => $request->expiry_date,
                            'item_num' => $request->item_num,
                            'vend_do' => $request->vend_do,
                            'vend_num' => $request->vend_num,
                            'qty_received' => null,
                            'qty_required' => null,
                            'qty_returned' => $request->qty,
                            'qty_returned_uom' => $request->uom,
                            'qty_conv' => $request->qty_conv,
                            'sap_base_entry' => $request->sap_base_entry,
                            'sap_base_line' => $request->sap_base_line,
                            'sync_status' => 1,
                            'site_id' => auth()->user()->site_id,
                            'created_by' => auth()->user()->name,
                            'modified_by' => auth()->user()->name,
                            'created_date' => now(),
                            'modified_date' => now(),
                        ]);
                    }
                    //$res = SapCallService::postPOReturn($request, null);
                    if ($sap_single_bin == 1) {

                        $result = SiteConnectionService::postIntergrationTransPO("PO Return", $request, null);
                        // $result = SapCallService::postPOReturn($request, null);
                    } else {
                        if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                            // Later Change to read from Matltrans
                            $result = SapCallService::postPOReturn($request, null);
                        } else {
                            $result = SapCallService::postPOReturn($request, null);
                        }
                    }
                    //dd($result);
                    if ($result != 200) {
                        if ($result == 404) {
                            // Goods Return Request does not exist in SAP.
                            Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_goods_return_request_notexist'))->persistent('Dismiss');
                        } else {
                            Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                        }

                        return redirect()->route('backshowPoLineReturn', ['whse_num' => $request->whse_num, 'po_line' => $request->ref_line, 'po_num' => $request->ref_num, 'vend_do' => $request->vend_do]);
                    }
                } else {

                    DB::table('sap_po_batch_sync')->insert([
                        'po_num' => $request->ref_num,
                        'po_line' => $request->ref_line,
                        'po_rel' => $request->ref_release ?? 0,
                        'reason_code' => $request->reason_code,
                        'is_return' => 1,
                        'doc_num' => $request->document_num,
                        'erp_ID' => PurchaseOrderItem::select('erp_ID')->where('po_num', $request->ref_num)
                            ->where('po_line', $request->ref_line)
                            //->where('po_rel', $po_rel)
                            ->where('site_id', auth()->user()->site_id)
                            ->value('erp_ID'),
                        'whse_num' => $request->whse_num,
                        'po_type' => 2,
                        'loc_num' => $request->loc_num,
                        'lot_num' => $request->lot_num,
                        'expiry_date' => $request->expiry_date,
                        'item_num' => $request->item_num,
                        'vend_do' => $request->vend_do,
                        'vend_num' => $request->vend_num,
                        'qty_received' => null,
                        'qty_required' => null,
                        'qty_returned' => $request->qty,
                        'qty_conv' => $request->qty_conv,
                        'qty_returned_uom' => $request->uom,
                        'sap_base_entry' => $request->sap_base_entry,
                        'sap_base_line' => $request->sap_base_line,
                        'sync_status' => 1,
                        'site_id' => auth()->user()->site_id,
                        'created_by' => auth()->user()->name,
                        'modified_by' => auth()->user()->name,
                        'created_date' => now(),
                        'modified_date' => now(),
                    ]);
                }
                //                $returnrequest_pending_list = DB::table('sap_po_batch_sync')
                //                        ->where('status', 'Pending')
                //                        ->where('vend_num', $request->vend_num)
                //                        ->where('doc_num', $request->document_num)
                //                        ->where('sync_status', "<", 3)
                //                        //->where('created_by', auth()->user()->name)
                //                        ->where('po_type', 2)
                //                        ->where('site_id', auth()->user()->site_id)
                //                        ->get();
                //                $arrProcess = $returnrequest_pending_list->toArray();
                //
                //                $arrBulkStore = array();
                //                foreach ($arrProcess as $key => $value) {
                //                    $uniqueKey = $value->vend_num . "-" . $value->po_num . "-" . $value->doc_num;
                //
                //                    $arrBulkStore[$uniqueKey][] = $arrProcess[$key];
                //
                ////            $arrBulkStore[$value->vend_num][] = $arrProcess[$key];
                //                }
                //                dd($arrBulkStore);
                //                 foreach ($arrBulkStore as $key => $arrProcess) {
                //
                ////                                    $res =SapCallService::postPOReturn($request);
                //
                //                    $result = SapCallService::postPOReturnBulk($request, $arrProcess);
                //                }
                //                dd($arrBulkStore,$request->input());
                //                $res =SapCallService::postPOReturn($request,null);
            }
            /*             * *********End SAP********************** */

            // Session::put('modulename', 'POReturn');
            // Session::put('po_num',$request->ref_num);


            DB::commit();
            // return app('App\Http\Controllers\RouteController')->gotoPOReturn();
            $input['ref_num'] = $request->ref_num;
            $input['whse_num'] = $request->whse_num;
            $input['po_line'] = $request->ref_line;
            $input['po_rel'] = $request->ref_release;
            $input['item_num'] = $request->item_num;
            $input['uom'] = $request->uom;
            $input['qty_returnable'] = $request->qty_returnable;
            $input['item_desc'] = $request->item_desc;
            $input['vend_do'] = $request->vend_do;
            $input['indicate'] = 0;
            //    return redirect()->route('showPoLineReturn', ['whse_num' => $request->whse_num, 'po_num' => $request->ref_num, 'item_num' => $request->item_num, 'vend_do' => $request->vend_do]);
            $url = generateRedirectUrl('POReturn', $input);
            // return $url;

            // Empty
            return redirect()->route('poReturn');
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public static function poReturnValidation($request)
    {
        $tparm = new TparmView;
        $errors = [];

        if ($request['grn_num']) {
            $result = ValidationController::checkGRNValidation($request);
            if ($result !== true) {
                $errors['grn_num'] = $result;
            } else {
                $list = GRNItem::with('grn')
                    ->where('grn_num', $request->grn_num)->where('grn_line', $request->grn_line)->where('site_id', auth()->user()->site_id)->first();

                // Rewrite the ReadOnly fields
                $request->merge([
                    'grn_num' => $list->grn_num,
                    'grn_line' => $list->grn_line,
                    'whse_num' => $list->grn->whse_num,
                    'item_num' => $list->item_num,
                    'base_uom' => $list->uom,
                    'vend_do' => $list->grn->vend_do,
                    'vend_num' => $list->grn->vend_num
                ]);
            }
        }

        if ($request['ref_num']) {
            $PoItemList = PurchaseOrderItem::where('po_num', $request->ref_num)->where('po_line', $request->ref_line)->where('site_id', auth()->user()->site_id)->first();

            // Rewrite the ReadOnly fields
            $request->merge([
                'ref_num' => $PoItemList->po_num,
                'ref_line' => $PoItemList->po_line,
                'ref_release' => $PoItemList->po_rel
            ]);

            $result = ValidationController::checkPOLineValidation($request);
            if ($result !== true) {
                $errors['ref_num'] = $result;
            }
        }
        if ($request['whse_num']) {
            $whse_num = $request['whse_num'];
            $result = ValidationController::checkWhseValidation($whse_num);
            if ($result !== true) {
                $errors['whse_num'] = $result;
            }
        }
        if ($request['item_num'] && $request['item_num'] != "NON-INV") {
            $item_num = $request['item_num'];
            $result = ValidationController::checkItemNumValidation($item_num, $request['whse_num']);

            if ($result !== true) {
                $errors['item_num'] = $result;
            }

            // check if item is catch weight
            $catch_weight = Item::where('item_num', $item_num)->value('catch_weight');
            if ($catch_weight == 1) {
                // Get Tolerance and UOM
                $tolerance = $PoItemList->qty_ordered - $PoItemList->qty_received + $PoItemList->qty_returned;
                $tolerance_uom = $PoItemList->uom;
                $request->merge([
                    'tolerance_uom' => $tolerance_uom,
                    'catch_weight' => $catch_weight,
                    'qty' => array_sum($request->arr_qty ?? []),
                    'base_uom' => Item::where('item_num', $item_num)->value('uom')
                ]);

                // Validate Catch Weight Data
                CatchWeightService::validateCatchWeightData($request, $tolerance, $tolerance_uom);
                return [$errors, $request];
            }
        }
        if ($request['loc_num']) {
            $disable_create_new_item_location = $tparm->getTparmValue('PoReceive', 'disable_create_new_item_location');

            $result = ValidationController::checkTransitPickingLocValidtion($request, 'loc_num', $disable_create_new_item_location);
            if ($result !== true) {
                $errors['loc_num'] = $result;
            }
        }
        if ($request['lot_num'] && $request['lot_tracked'] == 1) {
            $result = ValidationController::checkLotNumValidtion($request);
            if ($result !== true) {
                $errors['lot_num'] = $result;
            }
        }
        if ($request['qty']) {
            $result = ValidationController::checkLotLocQtyValidtion($request);
            if ($result !== true) {
                $errors['qty'] = $result;
            }
        }
        // if ($request['uom']) {
        //     $request['selected_uom'] = $request['uom'];
        //     $result = ValidationController::validateConvUOM($request);
        //     if ($result['result'] !== true) {
        //         $errors['uom'] = $result['msg'];
        //     }
        // }

        return [$errors, $request];
    }
}
