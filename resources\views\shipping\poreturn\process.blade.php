@extends('layout.mobile.app')

@section('content')
@section('title', __('Purchase Order Return'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }

    div.col-xs-2.col-md-1.col-lg-1,
    div.col-xs-2.col-md-1.col-lg-2 {
        margin-top: 5px;
    }
</style>
<div class="card-body collapse in">
    <div class="card-block">
        <form class="form" method="post" id="postform" action="{{ route('runPoReturnProcess') }}" autocomplete ="off">
            <input type="hidden" id="reason_class" value="POReturn" name="reason_class">
            <input type="hidden" name="non_inv" id="non_inv" value="{{ $non_inv }}">
            {{-- <input type="text" id="disable_create_new_item_location" name="disable_create_new_item_location" value="{{ $disable_create_new_item_location }}"> --}}
            @csrf
            <div class="form-body">
                @include('components.form.scan_input', ['type' => 'inventory'])

                @if (isset($batch_id))
                    <input type="hidden" name="batch_id" id="batch_id" value="{{ $batch_id }}" />
                @endif

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="po_num">{{ __('mobile.label.po_num') }}</label>
                    <div class="col-xs-5 col-md-7 col-lg-6">
                        <div class="input-group">
                            <input type="text" id="po_num" class="form-control border-primary"
                                value="{{ old('po_num', $po_item->po_num) }}" name="ref_num" readonly>
                            <input type="hidden" id="vend_num" class="form-control border-primary"
                                value="{{ old('vend_num', $po_item->vend_num) }}" name="vend_num" readonly>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;margin-top:0px">
                        <input type="text" id="po_line" class="form-control border-primary col-xs-2"
                            value="{{ old('po_line', $po_item->po_line) }}" name="ref_line" readonly>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style="padding:0px;">
                        <input style="width: 47px" type="text" id="po_rel" class="form-control border-primary"
                            value="{{ old('po_rel', $po_item->po_rel) }}" name="ref_release" hidden>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="item_num">{{ __('mobile.label.item_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" id="item_num" class="form-control border-primary"
                                value="{{ old('item_num', $po_item->item_num) }}" name="item_num" readonly>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control" for="item_desc"></label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <textarea readonly type="text" id="item_desc" class="form-control border-primary">{{ old('item_desc', $po_item->item_desc) }}</textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="whse_num">{{ __('mobile.label.whse_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" id="whse_num" class="form-control border-primary"
                                value="{{ old('whse_num', $po_item->whse_num) }}" name="whse_num" readonly>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="vend_do">{{ __('mobile.label.vend_do') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" id="vend_do" class="form-control border-primary"
                                value="{{ old('vend_do', $vendor_do ?? null) }}" name="vend_do" readonly>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control"
                        for="qty_returnable">{{ __('mobile.label.qty_returnable') }}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input style="text-align:right" type="text" id="qty_returnable" name="qty_returnable"
                                class="form-control border-primary"
                                value="{{ numberFormatPrecision($po_item->qty_returnable, $unit_quantity_format) }}"
                                readonly>
                            <input type="hidden" id="qty_returnable_conv" name="qty_returnable_conv"
                                value="{{ old('qty_returnable_conv', numberFormatPrecision($po_item->qty_returnable, $unit_quantity_format)) }}">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input class="form-control border-primary" id="ref_uom" name="ref_uom" readonly
                            value="{{ $po_item->uom }}">
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="vend_lot">{{ __('mobile.label.vend_lot') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input readonly type="text" id="vend_lot" class="form-control border-primary"
                                value="{{ old('vend_lot', $defaults['vend_lot']) }}"
                                placeholder="{{ __('mobile.placeholder.vend_lot') }}" name="vend_lot">
                        </div>
                    </div>
                </div>
                @if ($non_inv == 0)
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                {{-- If disable_lot_number_selection is 1 and item is lot tracked, it will be readonly --}}
                                <input type="text" @if ($disable_lot_number_selection == 1 && $po_item->item->lot_tracked == 1) readonly @endif
                                    onChange="onLocChange();clickSelf(this.id)" id="loc_num"
                                    class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.loc_num') }}" name="loc_num"
                                    value="{{ old('loc_num') }}"
                                    data-value="{{ old('loc_num', $defaults['loc_num']) }}">
                                <span id="checkLoc"></span>
                            </div>
                        </div>
                        {{-- If item is not lot tracked || item is lot tracked and disable_lot_number_selection is 0, this button will be shown --}}
                        @if ($po_item->item->lot_tracked == 0 || ($po_item->item->lot_tracked == 1 && $disable_lot_number_selection == 0))
                            <div class="col-xs-2 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                                <button type="button" name="{{ __('mobile.list.locations') }}"
                                    class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                    data-target="#myModal"
                                    onclick="selectionNull('/getItemLocCheckPicking','whse_num,item_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name)"><i
                                        class="icon-search"></i></button>
                            </div>
                        @endif
                    </div>
                @else
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                {{-- If disable_lot_number_selection is 1 and item is lot tracked, it will be readonly --}}
                                <input type="text" readonly class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.loc_num') }}">

                            </div>
                        </div>

                    </div>
                @endif
                @if ($non_inv == 0)
                    @if ($po_item->item->lot_tracked == 1)
                        <div class="form-group row">
                            <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                                for="lot_num">{{ __('mobile.label.lot_num') }}</label>
                            <div class="col-xs-7 col-md-8 col-lg-7">
                                <div class="input-group">
                                    {{-- If disable_lot_number_selection is 1, it will be readonly and predefined value --}}
                                    <input type="text"
                                        @if ($disable_lot_number_selection == 1) data-value="{{ old('lot_num', $defaults['lot_num']) }}" readonly
                                    @elseif($defaults['lot_num'])
                                        data-value="{{ old('lot_num', $defaults['lot_num']) }}" @endif
                                        value="{{ old('lot_num') }}" class="form-control border-primary"
                                        name="lot_num" id="lot_num" maxlength="50"
                                        placeholder="{{ __('mobile.placeholder.lot_num') }}">
                                    <span id="loc_info"></span>
                                </div>
                            </div>
                            {{-- If disable_lot_number_selection is 0, this button will be shown --}}
                            @if ($disable_lot_number_selection == 0)
                                <div class="col-xs-2 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                                    <button type="button" name="{{ __('mobile.list.lots') }}" tabindex="-1"
                                        onClick="selectionMultiLineInput('/getLotLocExpiry','whse_num,loc_num,item_num,sortField,sortBy','lot_num','loc_num,lot_num');modalheader_uom(this.id,this.name)"
                                        class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                        data-target="#myModal"><i
                                            class="icon-search"></i></button>
                                </div>
                            @endif
                        </div>
                    @endif
                @endif
                @include('components.form._qty_on_hand', [
                    'qty_available' => 0,

                    // 'qty_available' => numberFormatPrecision($defaults['qty_available'],$unit_quantity_format),
                    // 'qty_available_conv' => numberFormatPrecision(old('qty_available_conv', $defaults['qty_available_conv'],$unit_quantity_format)),
                    'qty_available_conv' => 0,

                    'base_uom' => old('base_uom', $defaults['base_uom']),
                ])

                <div class="form-group row">
                    <input type="hidden" id="allow_over_return" name='allow_over_return'
                        value="{{ old('allow_over_return', $allow_over_return) }}">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="qty_to_return">{{ __('mobile.label.qty_to_return') }}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input inputmode="numeric" type="text" style="text-align:right" name="qty" id="qty"
                                class="form-control border-primary number-format"
                                placeholder="{{ __('mobile.placeholder.qty_to_return') }}"
                                value="{{ numberFormatPrecision(old('qty'), $unit_quantity_format) }}">
                            <input type="hidden" name="max_qty_input" id="max_qty_input"
                                value="{{ numberFormatPrecision(old('max_qty_input'), $unit_quantity_format) }}">
                            <input type="hidden" name="conv_factor" id="conv_factor"
                                value="{{ old('conv_factor', 1) }}">
                            <input type="hidden" name="qty_need_to_convert" id="qty_need_to_convert"
                                value="{{ old('qty_need_to_convert') }}">
                            <input type="hidden" name="uom_need_to_convert" id="uom_need_to_convert"
                                value="{{ old('uom_need_to_convert') }}">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input class="form-control border-primary" id="uom" name="uom"
                            value="{{ old('uom', $po_item->uom) }}">
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style=" padding:0px;">
                        <button type="button" name="UOMs" id="UOMs"
                            onclick="selection('/getPOUOMConv','item_num,vend_num','uom','uom');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="reason">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="reason_code">{{ __('mobile.label.reason_code') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" autocomplete="off" value="{{ old('reason_code') }}"
                                name="reason_code" id="reason_code" class="form-control border-primary"
                                placeholder="Reason" onchange="clickSelf(this.id)">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" id="getRequest5" name="{{ __('mobile.list.reasons') }}"
                            onClick="selectionwithcheckreasoncode('/getReasonCode/POReturn', 'reason_code', 'reason_num', 'reason_code');modalheader(this.id, '{{__('admin.menu.reason_codes')}}');"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>
            </div>
            @if ($sap_trans_order_integration == 1)
                <div class="form-group row " id="sap_base_entry">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="sap_base_entry">{{ __('mobile.label.sap_base_entry') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" autocomplete="off" value="{{ old('sap_base_entry') }}" required
                                name="sap_base_entry" id="sap_base_entry" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.sap_base_entry') }}">
                        </div>
                    </div>

                </div>

                <div class="form-group row" id="sap_base_line">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="sap_base_entry">{{ __('mobile.label.sap_base_line') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" autocomplete="off" value="{{ old('sap_base_line') }}" required
                                name="sap_base_line" id="sap_base_line" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.sap_base_line') }}">
                        </div>
                    </div>
                </div>

                {{-- Document num --}}
                @if ($sap_trans_order_integration == 1)
                    <div class="form-group row" id="document_num">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="document_num">{{ __('mobile.label.doc') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" name="document_num" required id="document_num"
                                    autocomplete="off" class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.doc') }}"
                                    value="{{ old('document_num') }}" maxlength="30">
                            </div>
                        </div>
                    </div>
                @else
                    <div class="form-group row" id="document_num">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                            for="document_num">{{ __('mobile.label.doc') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" name="document_num" id="document_num" autocomplete="off"
                                    class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.doc') }}"
                                    value="{{ old('document_num') }}" maxlength="30">
                            </div>
                        </div>
                    </div>
                @endif

                {{-- Last Return? --}}
                <div class="form-group row" id="last_return_pair">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="last_return">{{ __('mobile.label.last_return') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="radio" name="last_return" value="Yes" id="last_return">
                            {{ __('mobile.option.yes') }} &emsp;&emsp;&emsp;
                            <input type="radio" name="last_return" value="No" id="last_return_no" checked>
                            {{ __('mobile.option.no') }}
                        </div>
                    </div>
                </div>

            @endif

            <div class="form-actions center">
                <button type="button" onClick="historyBack();" class="btn btn-warning mr-1">
                    <i class="icon-cross2"></i> {{ __('mobile.button.cancel') }}
                </button>
                <button type="submit" class="btn btn-primary submitloader">
                    <i class="icon-check2"></i> {{ __('mobile.button.process') }}
                </button>
            </div>
        </form>
    </div>
</div>

<style>
    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

@include('util.validate_uom')
@include('shipping.poreturn.POvalidation')
<script>
    var checkINV = "<?php echo $non_inv; ?>";
    if (checkINV == 0) {
        $('#qty').prop('readonly', true);
        $('#uom').prop('readonly', true);
        $('#UOMs').hide();
    } else {
        $('#qty').prop('readonly', false);
        $('#uom').prop('readonly', false);
        $('#UOMs').show();
        var return_value = $('#qty_returnable').val();
        $('#max_qty_input').val(return_value);
    }

    function onLocChange() {
        if (!$("#lot_num").is(":visible") && $("#loc_num").val()) {
            $('#qty').prop('readonly', false);
            $('#uom').prop('readonly', false);
            $('#UOMs').show();
            console.log("a");
        } else {
            if ($("#lot_num").is(":visible") && $("#lot_num").val()) {
                $('#qty').prop('readonly', false);
                $('#uom').prop('readonly', false);
                $('#UOMs').show();
                   console.log("b");
            } else {
                $('#qty').prop('readonly', true);
                $('#uom').prop('readonly', true);
                $('#UOMs').hide();
            }


            // $("#qty_available, #lot_num, #qty").val('')
        }
    }

    $(document).ready(function() {

        $("#loc_num").change();
        $("#lot_num").on('change', function() {
            if ($("#lot_num").val()) {
                $.ajax({
                    url: '{{ route('getVendLot') }}',
                    type: 'GET',
                    data: {
                        lot_num: $("#lot_num").val(),
                    },
                    success: function(data) {
                        if (data) {
                            $("#vend_lot").val(data);
                        } else {
                            $("#vend_lot").val('');
                        }
                        console.log('sini' + $('#vend_num').val());
                        display('/displayLotQuantityCoPick',
                            'item_num,whse_num,loc_num,lot_num,qty_returnable,ref_uom,cust_num,uom,vend_num',
                            'qty_available,base_uom,qty_available_conv,max_qty_input,qty_need_to_convert,uom_need_to_convert'
                        );
                        $('#qty').prop('readonly', false);
                        $('#uom').prop('readonly', false);
                        $('#UOMs').show();
                    }
                });
            }
        });

        $("#uom").on("change", function() {
            var lot_num = "undefined";
            if ($("#lot_num").val() != "") {
                lot_num = $("#lot_num").val();
            }
            var vend_num = "null";
            if ($("#vend_num").val() != "") {
                vend_num = $("#vend_num").val();
            }
            var cust_num = 'null';
            // ajaxurl ="{{ route('getAllUOMConv', ['item_num', 'base_uom', 'cust_num', 'vend_num']) }}";
            // url = ajaxurl.replace('item_num', btoa($("#item_num").val()));
            // url = url.replace('base_uom', btoa($("#base_uom").val()));
            // url = url.replace('cust_num', btoa(cust_num));
            // url = url.replace('vend_num', btoa(vend_num));
            var selectuom = $("#uom").val();
            var uomConv = [];
            if (selectuom != "") {

                let validate = validateConvUOM($('#item_num').val(), null, $("#vend_num").val(), $(
                    '#ref_uom').val(), $('#base_uom').val(), selectuom);

                validate.then(function(resp) {
                        // true
                        $("#qty").val('');
                    },
                    function(err) {
                        // false
                        $("#qty").val('');
                        $("#uom").val($("#base_uom").val());
                    }).finally(function() {
                        if($("#item_num").val() == "NON-INV") {
                            displayConvQty('/displayQuantityConverted','base_uom,item_num,qty_available,uom','qty_available_conv');
                            displayConvQty('/displayQuantityConverted','base_uom,item_num,qty_returnable,uom','qty_returnable_conv');
                        } else {
                            // conv qty available
                            calculateQtyLimit($("#base_uom").val(), $("#qty_available")
                                .val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(),
                                $("#loc_num").val(), lot_num, $("#base_uom").val(), cust_num,
                                vend_num, "{{ __('mobile.nav.po_return') }}", "", "qty_available_conv");
                            // conv qty returnable
                            calculateQtyLimit($("#ref_uom").val(), $("#qty_returnable")
                                .val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(),
                                $("#loc_num").val(), lot_num, $("#base_uom").val(), cust_num,
                                vend_num, "{{ __('mobile.nav.po_return') }}", "", "qty_returnable_conv");
                        }
                });

                // $.get(url, function(data){
                //     console.log(JSON.stringify(data));
                //     data.forEach(element => {
                //         uomConv.push(element['uom']);
                //     });

                //     if(jQuery.inArray(selectuom, uomConv) != -1){
                //         $("#qty").val('');
                //         calculateQtyLimit($("#uom_need_to_convert").val(), $("#qty_need_to_convert").val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(), $("#loc_num").val(), lot_num, $("#base_uom").val(), cust_num, vend_num, "{{ __('mobile.nav.po_return') }}", "");
                //     }
                //     else{
                //         $("#qty").val('');
                //         $("#uom").val($("#base_uom").val());
                //         Alert.notexist('{{ __('admin.label.uom') }}', $("#uom").val());
                //     }
                // });
            }
            // if ($(this).val()) {
            //     displayConvQty('/displayQuantityConverted','base_uom,item_num,qty_available,uom','qty_available_conv');
            //     displayConvQty('/displayQuantityConverted','ref_uom,item_num,qty_returnable,uom','qty_returnable_conv');
            // }
        });
        $("#uom").trigger('change');

        /*$("#loc_num").on("change", function() {
            $("#qty_available-error,#qty_returnable-error,#loc_num-error").remove();
            $("#loc_info").html("");
            $("#checkLoc").html("");
            $("#locnumnotexist").html('');

            if ($("#loc_num").val() == "") {
                $("#loc_info").html("");
                $("#checkLoc").html("");
                $("#locnumnotexist").html('');
            } else {
                // Send error if manually type object that is transit location
                $.ajax({
                    url: '{{ route('checkLocNotTransitpickLocs') }}',
                    type: "GET",
                    data: {
                        whse_num: $("#whse_num").val(),
                        loc_num: $("#loc_num").val(),
                    },
                    success: function(data) {
                        console.log(data);
                         if(data.length > 0){
                            if(data[0].pick_locs==1)
                                {
                                 //   document.getElementById('loc_num-error').style.display = 'none';
                                     $("#checkLoc").html(
                                '<span style="color:red;"> {{ __('error.mobile.validate_picking') }}</span>'
                                );
                            $("#loc_info").html("");
                            $("#locnumnotexist").html('');
                            $("#qty").val("");
                            $(".submitloader").attr("disabled", true);


                                  //  $("#loc_info").html("");
                                   // $("#locnumnotexist").html('');
                                  //  $("#qty").val("");
                                  //  $("#qty_available").val("0");
                                    //errorMessage = "{{ __('error.mobile.validate_picking') }}";
                                  //  $("#checkLoc").html('<span style="color:red;"> {{ __('error.mobile.validate_picking') }}</span>');
                                   //  $(".submitloader").attr("disabled", true);
                                    //return false;
                                }
                       else if (data[0].loc_type == "T") {
                       // document.getElementById('loc_num-error').style.display = 'none';
                            $("#checkLoc").html(
                                '<span style="color:red;"> {{ __('error.mobile.validate_transit') }}</span>'
                                );
                            $("#loc_info").html("");
                            $("#locnumnotexist").html('');
                            $("#qty").val("");
                            $(".submitloader").attr("disabled", true);
                        } else {
                            display('/displayQuantityPOReturn',
                                'item_num,whse_num,loc_num,qty_returnable,ref_uom,null,uom,vend_num',
                                'qty_available,base_uom,qty_available_conv,max_qty_input,qty_need_to_convert,uom_need_to_convert'
                                );
                            $("#loc_info").html('');
                            $("#checkLoc").html('');
                            $("#locnumnotexist").html('');
                            // showNewLoc();
                            $(".submitloader").attr("disabled", false);
                        }

                    }
                    else{

                        document.getElementById('loc_num-error').style.display = 'none';
                                      $("#checkLoc").html(
                                '<span style="color:red;"> {{ __('error.mobile.loc_not_exists') }}</span>'
                                );
                            $("#loc_info").html("");
                            $("#locnumnotexist").html('');
                            $("#qty").val("");
                            $(".submitloader").attr("disabled", true);

                    }
                    }
                });
            }
        });*/
    });

    function submitForm(payload) {
        var form = document.createElement('form');
        form.style.visibility = 'hidden';
        form.method = 'POST';
        form.action = "{{ route('showPoLineReturn') }}";

        payload._token = $("[name=_token]").val();

        if (payload.butt_name) {
            delete payload.butt_name;
        }

        $.each(payload, function(i, v) {
            var input = document.createElement('input');
            input.name = i;
            input.value = v;
            form.appendChild(input); // add key/value pair to form
        });

        document.body.appendChild(form); // forms cannot be submitted outside of body
        form.submit(); // send the payload and navigate
    }

    function historyBack() {

         if (document.location.href.includes("po-return/list"))
         {
        var payload = <?= json_encode(Session::get('request_data_poreturn')) ?>;
        submitForm(payload);
        }
         else
         {

           var URLRedirect = "{{$url}}";
           url = URLRedirect.replace(/&amp;/g, "&");
            //  window.history.go(-1);
           // window.location.href = '/home/<USER>/po-return';
           // alert(url);
             window.location.href = url;
         }
    }
</script>
@include('util.selection')
@include('errors.maxchar')
@include('Pallet.palletMobileValidation')


@endsection()
