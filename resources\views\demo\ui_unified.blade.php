@extends('layout.mobile.app')
@section('title', 'Unified Single-Page Form Demo')
@section('content')
<style>
    .form-section {
        margin-bottom: 1.5rem;
    }
    .barcode-input {
        position: relative;
    }
    .barcode-input i {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        color: #007bff;
    }
    @media (max-width: 576px) {
        .form-section label {
            font-size: 1rem;
        }
    }
</style>
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Stock Move (Unified Form Demo)</h5>
    </div>
    <div class="card-body">
        <form autocomplete="off">
            <div class="form-section">
                <label for="barcode" class="form-label">Scan QR/Barcode</label>
                <div class="barcode-input">
                    <input type="text" class="form-control" id="barcode" placeholder="Scan here...">
                    <i class="icon-barcode"></i>
                </div>
            </div>
            <div class="form-section row">
                <div class="col-md-6 mb-2">
                    <label for="whse_num" class="form-label">Warehouse</label>
                    <input type="text" class="form-control" id="whse_num" placeholder="Warehouse Number">
                </div>
                <div class="col-md-6 mb-2">
                    <label for="item_num" class="form-label">Item</label>
                    <input type="text" class="form-control" id="item_num" placeholder="Item Number">
                </div>
            </div>
            <div class="form-section row">
                <div class="col-md-6 mb-2">
                    <label for="from_loc" class="form-label">From Location</label>
                    <input type="text" class="form-control" id="from_loc" placeholder="Scan or enter location">
                </div>
                <div class="col-md-6 mb-2">
                    <label for="to_loc" class="form-label">To Location</label>
                    <input type="text" class="form-control" id="to_loc" placeholder="Scan or enter location">
                </div>
            </div>
            <div class="form-section row">
                <div class="col-md-6 mb-2">
                    <label for="qty" class="form-label">Quantity</label>
                    <input type="number" class="form-control" id="qty" placeholder="Qty">
                </div>
                <div class="col-md-6 mb-2">
                    <label for="uom" class="form-label">UOM</label>
                    <select class="form-control" id="uom">
                        <option>EA</option>
                        <option>BOX</option>
                        <option>PALLET</option>
                    </select>
                </div>
            </div>
            <div class="form-section">
                <label for="remarks" class="form-label">Remarks</label>
                <textarea class="form-control" id="remarks" rows="2" placeholder="Optional"></textarea>
            </div>
            <div class="d-flex justify-content-between align-items-center mt-4">
                <button type="reset" class="btn btn-outline-secondary">Reset</button>
                <button type="submit" class="btn btn-primary">Submit</button>
            </div>
        </form>
    </div>
</div>
<script>
    // Simulate barcode focus on page load for handhelds
    document.getElementById('barcode').focus();
</script>
@endsection
