@extends('layout.mobile.app')
@section('title', 'Stepper/Wizard Form Demo')
@section('content')
<style>
    .step {
        display: none;
    }
    .step.active {
        display: block;
    }
    .step-indicator {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
    }
    .step-indicator .circle {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #007bff;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
    }
    .step-indicator .label {
        font-size: 0.9rem;
    }
    .step-indicator .circle.inactive {
        background: #d6d6d6;
        color: #888;
    }
</style>
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">CO Return (Stepper Demo)</h5>
    </div>
    <div class="card-body">
        <div class="step-indicator mb-3">
            <div class="text-center">
                <div class="circle step1">1</div>
                <div class="label">Filter</div>
            </div>
            <div class="text-center">
                <div class="circle step2 inactive">2</div>
                <div class="label">List</div>
            </div>
            <div class="text-center">
                <div class="circle step3 inactive">3</div>
                <div class="label">Process</div>
            </div>
        </div>
        <form id="stepperForm" autocomplete="off">
            <div class="step active" id="step-1">
                <h6>Step 1: Filter</h6>
                <div class="mb-3">
                    <label for="filter_item" class="form-label">Item</label>
                    <input type="text" class="form-control" id="filter_item" placeholder="Scan or enter item">
                </div>
                <div class="mb-3">
                    <label for="filter_date" class="form-label">Date</label>
                    <input type="date" class="form-control" id="filter_date">
                </div>
                <button type="button" class="btn btn-primary float-end" onclick="nextStep()">Next</button>
            </div>
            <div class="step" id="step-2">
                <h6>Step 2: Select from List</h6>
                <div class="mb-3">
                    <label class="form-label">Select Entry</label>
                    <select class="form-control">
                        <option>Entry 1</option>
                        <option>Entry 2</option>
                        <option>Entry 3</option>
                    </select>
                </div>
                <button type="button" class="btn btn-secondary" onclick="prevStep()">Back</button>
                <button type="button" class="btn btn-primary float-end" onclick="nextStep()">Next</button>
            </div>
            <div class="step" id="step-3">
                <h6>Step 3: Process Form</h6>
                <div class="mb-3">
                    <label for="process_qty" class="form-label">Quantity</label>
                    <input type="number" class="form-control" id="process_qty" placeholder="Qty">
                </div>
                <div class="mb-3">
                    <label for="process_remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="process_remarks" rows="2"></textarea>
                </div>
                <button type="button" class="btn btn-secondary" onclick="prevStep()">Back</button>
                <button type="submit" class="btn btn-primary float-end">Submit</button>
            </div>
        </form>
    </div>
</div>
<script>
    let currentStep = 1;
    function showStep(step) {
        document.querySelectorAll('.step').forEach((el, idx) => {
            el.classList.remove('active');
            if (idx === step - 1) el.classList.add('active');
        });
        document.querySelector('.step1').classList.toggle('inactive', step !== 1);
        document.querySelector('.step2').classList.toggle('inactive', step !== 2);
        document.querySelector('.step3').classList.toggle('inactive', step !== 3);
    }
    function nextStep() {
        if (currentStep < 3) currentStep++;
        showStep(currentStep);
    }
    function prevStep() {
        if (currentStep > 1) currentStep--;
        showStep(currentStep);
    }
    showStep(currentStep);
</script>
@endsection
