@extends('layout.mobile.app')
@section('title', 'Tabbed/Segmented Form Demo')
@section('content')
<style>
    .nav-tabs .nav-link.active {
        background-color: #007bff;
        color: #fff;
    }
    .tab-content {
        margin-top: 1rem;
    }
</style>
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Batch Stock Move (Tabbed Form Demo)</h5>
    </div>
    <div class="card-body">
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="main-tab" data-bs-toggle="tab" data-bs-target="#main" type="button" role="tab">Main</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab">Details</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="remarks-tab" data-bs-toggle="tab" data-bs-target="#remarks" type="button" role="tab">Remarks</button>
            </li>
        </ul>
        <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active" id="main" role="tabpanel">
                <div class="mb-3">
                    <label for="batch_id" class="form-label">Batch ID</label>
                    <input type="text" class="form-control" id="batch_id" placeholder="Scan or enter batch">
                </div>
                <div class="mb-3">
                    <label for="whse_num" class="form-label">Warehouse</label>
                    <input type="text" class="form-control" id="whse_num" placeholder="Warehouse Number">
                </div>
            </div>
            <div class="tab-pane fade" id="details" role="tabpanel">
                <div class="mb-3">
                    <label for="item_num" class="form-label">Item</label>
                    <input type="text" class="form-control" id="item_num" placeholder="Scan or enter item">
                </div>
                <div class="mb-3">
                    <label for="qty" class="form-label">Quantity</label>
                    <input type="number" class="form-control" id="qty" placeholder="Qty">
                </div>
            </div>
            <div class="tab-pane fade" id="remarks" role="tabpanel">
                <div class="mb-3">
                    <label for="remarks" class="form-label">Remarks</label>
                    <textarea class="form-control" id="remarks" rows="3"></textarea>
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-between align-items-center mt-4">
            <button type="reset" class="btn btn-outline-secondary">Reset</button>
            <button type="submit" class="btn btn-primary">Submit</button>
        </div>
    </div>
</div>
<script>
    // Bootstrap 5 tab activation (if not already handled globally)
    var triggerTabList = [].slice.call(document.querySelectorAll('#myTab button'));
    triggerTabList.forEach(function (triggerEl) {
      var tabTrigger = new bootstrap.Tab(triggerEl);
      triggerEl.addEventListener('click', function (event) {
        event.preventDefault();
        tabTrigger.show();
      });
    });
</script>
@endsection
