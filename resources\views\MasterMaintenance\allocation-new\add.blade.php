@extends('layout.app')
@section('content')
<div class="card">
    <div class="card-header1">
        <h4 class="card-title" id="basic-layout-colored-form-control">{{__('admin.title.add_allocation')}}</h4>
    </div>
    <br>
    <div class="msgsuccess alert alert-success" style="display: none;">{{__('success.deleted', ['resource' => __('admin.label.order_line')])}}</div>
    @if (session('successmsg'))
        <div class="alert alert-success">{{ session('successmsg') }}</div>
    @endif
    @if (session('errormsg'))
        <div class="alert alert-danger">{{ session('errormsg') }}</div>
    @endif
    <form id="form_allocation" autocomplete="off" action="{{ route('allocation.allocate') }}" method="POST">
        @csrf
        {{-- might be changed using ajax later on (TO BE CONTINUED) --}}
        <input hidden name="pending" value="true">
        <div class="form-body">
            <div class="form-group">
                <table>
                    <tr>
                        <td width='150px'><label for="whse" class="required">{{__('admin.label.whse_num')}}</label></td>
                        <td width='300px'><input type="text" name="whse_num" id="whse_num" tabindex="1" class="form-control border-primary" placeholder="{{__('admin.label.whse_num')}}" value="{{auth()->user()->getCurrWhse()}}" {{$enable_warehouse == 0 ? 'readonly' : ''}} required></td>
                        @if ($enable_warehouse == 1)
                            <td width='100px'><button type="button" name="{{__('admin.list.warehouses')}}" tabindex="-1" onClick="selection('/getWhse','whse_num', 'whse_num', 'whse_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td>
                        @endif
                    </tr>
                    <tr>
                        <td width='140px'>
                            <label for="order_type">{{__('admin.label.order_type')}}</label>
                        </td>
                        <td colspan="5">
                            <!-- Show Customer Order if have Allocation -->
                            @if (in_array("MAINTENANCE_ALLOCATION_CO",$allocation_groups) || in_array("MAINTENANCE_ALLOCATION", $allocation_groups))
                            {{__('admin.option.co_num')}}
                                <div class="form-check-inline">

                                    <label class="custom-control custom-radio">
                                        {{-- <input type="hidden" name="type[]" value="Customer Order"> --}}
                                        <input type="radio" tabindex="2" class="custom-control-input checkbox-order-type" checked name="type[]" value="Customer Order">
                                         {{--    <span class="custom-control-indicator"></span>
                                            <span class="custom-control-description">{{__('admin.option.co_num')}}</span> --}}
                                    </label>
                                </div>
                            @endif
                            {{-- @if (in_array("MAINTENANCE_ALLOCATION_JO",$allocation_groups))
                                @if($plan_code != 'AX-MT-STR-M' && $plan_code != 'AX-MT-STR-A')
                                    <div class="form-check-inline">
                                        <label class="custom-control custom-radio">
                                            <input type="radio" tabindex="3" class="custom-control-input checkbox-order-type"  name="type[]" value="Job Order">
                                                <span class="custom-control-indicator"></span>
                                                <span class="custom-control-description">{{__('admin.option.JobOrder')}}</span>
                                        </label>
                                    </div>
                                @endif
                            @endif
                            @if($plan!=7 && $plan!=1 && $plan!=4)
                                @if (in_array("MAINTENANCE_ALLOCATION_TO",$allocation_groups))
                                    @if($plan_code != 'AX-MT-FREE-M' && $plan_code != 'AX-MT-STR-M' && $plan_code != 'AX-MT-STR-A')
                                        <div class="form-check-inline">
                                            <label class="custom-control custom-radio">
                                                <input type="radio" tabindex="4" class="custom-control-input checkbox-order-type"  name="type[]" value="Transfer Order">
                                                    <span class="custom-control-indicator"></span>
                                                    <span class="custom-control-description">{{__('admin.option.trans_num')}}</span>
                                            </label>
                                        </div>
                                    @endif
                                @endif
                            @endif --}}
                        </td>
                    </tr>
                    <tr>
                        <td width='140px'><label for="order_num">{{__('admin.label.ref_num')}}</label></td>
                        <td width='250px'><input type="text" name="from_order_num" id="from_order_num"  tabindex="5" class="form-control border-primary" placeholder="{{__('admin.label.from_variable',['variable' => __('admin.label.ref_num')])}}" ></td>
                        <td width='100px'><button type="button" name="Order Numbers" id="btn_from_order_num" tabindex="-1" class="btn-magnify btn-icon btn-magnify-class btn-order-num" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td>
                        <td width='50px'><label> {{__('admin.label.to')}}</label></td>
                        <td width='250px'><input type="text" name="to_order_num" id="to_order_num" tabindex="10" class="form-control border-primary" placeholder="{{__('admin.label.to_variable',['variable' => __('admin.label.ref_num')])}}" ></td>
                        <td><button type="button" name="Order Numbers" id="btn_to_order_num" class="btn-magnify btn-icon btn-magnify-class btn-order-num" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td>
                    </tr>
                    <tr>
                        <td><label for="due_date">{{__('admin.label.due_date')}}</label></td>
                        <td><input type="text" id="from_due_date" tabindex="6" class="form-control border-primary input-group from_due_date" name="from_due_date" placeholder="From Due Date" value="{{ old('due_date') }}" ></td>
                        <td>
                        </td>
                        <td width='50px'><label> {{__('admin.label.to')}}</label></td>
                        <td><input type="text" id="to_due_date" tabindex="11" class="form-control border-primary input-group to_due_date" name="to_due_date" placeholder="To Due Date" value="{{ old('due_date') }}" ></td>
                    </tr>
                    <tr>
                        <td><label for="item_num">{{__('admin.label.item')}}</label></td>
                        <td><input type="text" name="from_item_num" id="from_item_num" tabindex="7" class="form-control border-primary" placeholder="From Item" ></td>
                        <td>
                            <button type="button" name="{{__('admin.label.item_num')}}" onClick="selection('/getItem','from_item_num','item_num','from_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> {{__('admin.label.to')}}</label></td>
                        <td><input type="text" name="to_item_num" id="to_item_num" tabindex="12" class="form-control border-primary" placeholder="To Item" ></td>
                        <td>
                            <button type="button" name="{{__('admin.label.item_num')}}" onClick="selection('/getItem','to_item_num','item_num','to_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="product_code">{{ __('admin.label.product_code') }} &nbsp;</label></td>
                        <td><input type="text" name="from_product_code" id="from_product_code"   tabindex="8" class="form-control border-primary" placeholder="From Product Code" ></td>
                        <td>
                            <button type="button" name="Product Codes" onClick="selection('/getProdCode','from_product_code', 'product_code', 'from_product_code');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> {{__('admin.label.to')}}</label></td>
                        <td><input type="text" name="to_product_code" id="to_product_code" tabindex="13" class="form-control border-primary" placeholder="To Product Code" ></td>
                        <td>
                            <button type="button" name="Product Codes" onClick="selection('/getProdCode','to_product_code', 'product_code', 'to_product_code');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="cust_num">{{__('admin.label.cust')}}</label></td>
                        <td><input type="text" name="from_cust_num" id="from_cust_num" tabindex="9" class="form-control border-primary" placeholder="From Customer" ></td>
                        <td>
                            <button type="button" name="cust_num" onClick="selection('/getCust','from_cust_num', 'cust_num', 'from_cust_num');modalheader(this.id,'Customers');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> {{__('admin.label.to')}}</label></td>
                        <td><input type="text" name="to_cust_num" id="to_cust_num" tabindex="14" class="form-control border-primary" placeholder="To Customer" ></td>
                        <td>
                            <button type="button" name="cust_num" onClick="selection('/getCust','to_cust_num','cust_num','to_cust_num');modalheader(this.id,'Customers');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="form-actions center">
                <button type="button" id="cancel" class="cancel btn btn-warning mr-1" onclick="location.href='{{ route('allocation.index') }}'">
                    <i class="icon-cross2"></i> {{__('admin.button.cancel')}}
                </button>
                <button type='button' id="filter" class="btn btn-primary mr-1" data-toggle="modal" data-target="#modal_filtered">
                    <i class="icon-filter2"></i> {{__('admin.button.filter')}}
                </button>
                <button type="reset" id="btn-reset" class="btn btn-primary mr-1">
                    <i class="icon-refresh2"></i> {{__('admin.button.reset')}}
                </button>
                <button type="submit" id="btn-run-allocation" class="btn btn-info mr-1">
                    <i class="icon-check2"></i> {{__('admin.label.run_allocation')}}
                </button>
            </div>
        </div>
    </form>
    <!-- Modal filtered -->
    <div class="modal fade" id="modal_filtered" tabindex="-1" role="dialog" aria-labelledby="modal_filtered" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h5 class="modal-title" id="modal_filteredd">{{__('admin.label.filtered_order_lines')}} </h5>
                </div>
                <div class="modal-body">
                         <div class="alert alert-danger hidden" id="generate_error"></div>

                    {{-- <label class="text-danger hidden" id="generate_error"></label> --}}
                    <table class="table table-bordered table-hover nowrap" id="generate">
                        <thead>
                            <tr>
                                <th></th>
                                <th>{{__('admin.label.order_type')}}</th>
                                <th>{{__('admin.label.whse_num')}}</th>
                                <th>{{__('admin.label.ref_num')}}</th>
                                <th>{{__('admin.label.suffix')}}</th>
                                <th>{{__('admin.label.ref_line')}}</th>
                                <th>{{__('admin.label.ref_release')}}</th>
                                <th>{{__('admin.label.item_num')}}</th>
                                <th>{{__('admin.label.item_desc')}}</th>
                                <th>{{__('admin.label.qty_required')}}</th>
                                <th>{{__('admin.label.qty_available')}}</th>
                                <th>{{__('admin.label.qty_variance')}}</th>
                                <th>{{__('admin.label.qty_shipped')}}</th>
                                <th>{{__('admin.label.qty_released')}}</th>
                                <th>{{__('admin.label.uom')}}</th>
                                <th>{{__('admin.label.due_date')}}</th>
                                <th>{{__('admin.label.product_code')}}</th>
                                <th>{{__('admin.label.cust_num')}}</th>
                                <th>{{__('admin.label.shipping_zone')}}</th>
                            </tr>
                            <tr>
                                <th><input type='checkbox' class="generate-row-all"></th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:80%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th>
                                    {{-- <input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."> --}}
                                </th>
                                  <th>
                                    {{-- <input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."> --}}
                                </th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:65%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-warning" data-dismiss="modal">
                        <i class="icon-cross2"></i> {{__('admin.button.cancel')}}
                    </button>
                    <button type="button" class="save btn btn-primary" data-dismissed="modal">
                        <i class="icon-plus"></i> {{__('admin.button.add')}}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div style="margin-bottom:-10px">
    <h4 class="card-title" style="margin-top:-18px;" id="basic-layout-colored-form-control">{{__('admin.label.selected_order_lines')}}</h4>
</div>
<br>
<div>
    <table class="table table-bordered table-hover nowrap" id="pending">
        <thead>
            <tr>
                <th></th>
                <th>{{__('admin.label.order_type')}}</th>
                <th>{{__('admin.label.whse_num')}}</th>
                <th>{{__('admin.label.ref_num')}}</th>
                <th>{{__('admin.label.suffix')}}</th>
                <th>{{__('admin.label.ref_line')}}</th>
                <th>{{__('admin.label.ref_release')}}</th>
                <th>{{__('admin.label.item_num')}}</th>
                <th>{{__('admin.label.item_desc')}}</th>
                <th>{{__('admin.label.qty_required')}}</th>
                <th>{{__('admin.label.qty_available')}}</th>
                <th>{{__('admin.label.qty_variance')}}</th>
                <th>{{__('admin.label.qty_shipped')}}</th>
                <th>{{__('admin.label.qty_released')}}</th>
                <th>{{__('admin.label.uom')}}</th>
                <th>{{__('admin.label.due_date')}}</th>
                <th>{{__('admin.label.product_code')}}</th>
                <th>{{__('admin.label.cust_num')}}</th>
                <th>{{__('admin.label.shipping_zone')}}</th>
                <th>{{__('admin.label.action')}}</th>
            </tr>
            <tr>
                <th><input type='checkbox' class='pending-row-all'></th>
                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:70%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:70%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:70%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:70%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:65%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
                <th></th>
            </tr>
        </thead>
    </table>
</div>
<button type="button" id="delete" class="btn btn-danger mr-1" style="margin-bottom:3rem !important">
    <i class="icon-trash2"></i> {{__('admin.button.delete')}}
</button>

    <script type="text/javascript">

        $(document).ready(function(){
            $("#modal_filtered").on('hidden.bs.modal',function(){

            })
            unit_quantity_format = "{{$unit_quantity_format}}";

            // Filter selection of order
            $(".btn-order-num").on('click',function() {
                var check_order_type = "";
                $(".checkbox-order-type:checked").each(function() {
                    check_order_type = check_order_type + $(this).val() + '-';
                });

                $("#check_order_type").remove(); // To avoid duplicate
                $("#form_allocation").append("<input type='hidden' name='check_order_type' id='check_order_type' value='"+check_order_type+"'>")

                if (this.id == "btn_from_order_num") {
                    selection('/getOrderNumBasedOnType','check_order_type,from_order_num', 'order_num', 'from_order_num');modalheader(this.id, this.name);
                }
                else if (this.id == "btn_to_order_num") {
                    selection('/getOrderNumBasedOnType','check_order_type,to_order_num', 'order_num', 'to_order_num');modalheader(this.id, this.name);
                }
            });

            // Filter datatable based on the column
            $("#pending thead input").on('keyup', function (e) {
                // Press Enter then draw
                if (e.keyCode == 13) {
                                     var index=$(this).parent().index();

                       var val = this.value;
                    var val2 = parseFloat(val.replace(/,/g, ""));

                    if (isNaN(val2)) {
                        $('#pending').DataTable().column(index).search(val).draw();
                    } else {
                         $('#pending').DataTable().column(index).search(val2).draw();
                    }
                    // $('#pending').DataTable().column($(this).parent().index()).search(this.value).draw();
                }
            });
            $("#generate thead input").on('keyup', function (e) {
                // Press Enter then draw
                if (e.keyCode == 13) {
                                    dataTableSearch('#generate',$(this).parent().index() + ':visible',this.value);

//                  var index=$(this).parent().index();
//                     if(index>13)
//                      index=index+1;
//                     var val = this.value;
//                     var val2 = parseFloat(val.replace(/,/g, ""));
// console.log(val2,val,index);
//                     if (isNaN(val2)) {
//                         $('#generate').DataTable().column(index).search(val).draw();
//                     } else {
//                          $('#generate').DataTable().column(index).search(val2).draw();
//                     }
                    // $('#generate').DataTable().column(index).search(this.value).draw();
                }
            });

            // Show pending datatable
            $('#pending').DataTable({
                scrollX: true, // enable scroll horizontal
                destroy: true,
                processing: true, // loading bar
                autoWidth: true,
                serverSide: false,
                responsive: true,
                searching: true,
                bSortCellsTop: true, // set ordering icon at the first thead trPAll
                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
                ordering: true,
                order: [], // set to empty array to remove the icon of sorting on the first column
                ajax: {
                    url: "{{route('allocation.pending')}}",
                    dataSrc: function (json) {
                        var result = new Array();
                        for (i = 0; i < json.data.length; i++) {
                            result.push({
                                'id': "<input type='checkbox' class='pending-row-data' value='"+json.data[i].id+"'>",
                                'order_type': json.data[i].order_type,
                                'whse_num': json.data[i].whse_num,
                                'ref_id': json.data[i].ref_id,
                                'ref_num': json.data[i].ref_num,
                                'suffix': json.data[i].suffix,
                                'ref_line': json.data[i].ref_line,
                                'ref_release': json.data[i].ref_release,
                                'item_num': json.data[i].item_num,
                                'item_desc': json.data[i].item?json.data[i].item.item_desc:"",
                                'qty_required': numberFormatPrecision(json.data[i].qty_required,unit_quantity_format),
                                'qty_available': numberFormatPrecision(json.data[i].qty_available,unit_quantity_format),
                                'qty_shortage' : "<div class='td-qty-shortage'>"+numberFormatPrecision(json.data[i].qty_shortage,unit_quantity_format)+"</div>",
                                'qty_shipped': numberFormatPrecision(json.data[i].qty_shipped,unit_quantity_format),
                                'qty_released': json.data[i].qty_released,
                                'uom': json.data[i].uom,
                                'due_date': json.data[i].due_date,
                                'product_code': json.data[i].item?json.data[i].item.product_code:"",
                                'cust_num': json.data[i].cust_num,
                                'shipping_zone': json.data[i].shipping_zone,
                                'action': '<button type="button" class="btn btn-danger delete" id="'+json.data[i].id+'"><i class="icon-trash"></i></button>',
                            });
                        }
                        return result;
                    }
                },
                columns: [
                    { data: 'id', name: 'id', width:"1%", orderable: false},
                    { data: 'order_type', name: 'order_type' },
                    { data: 'whse_num', name: 'whse_num'},
                    { data: 'ref_num', name: 'ref_num', className: 'wrapText'},
                    { data: 'suffix', name: 'suffix'},
                    { data: 'ref_line', name: 'ref_line'},
                    { data: 'ref_release', name: 'ref_release'},
                    { data: 'item_num', name: 'item_num'},
                    { data: 'item_desc', name: 'item_desc' },
                    { data: 'qty_required', name: 'qty_required', className: 'text-right'},
                    { data: 'qty_available', name: 'qty_available', className: 'text-right'},
                    { data: 'qty_shortage', name: 'qty_shortage',className: 'text-right'},
                    { data: 'qty_shipped', name: 'qty_shipped', className: 'text-right'},
                    { data: 'qty_released', name: 'qty_released', className: 'text-right', visible: false},
                    { data: 'uom', name: 'uom'},
                    { data: 'due_date', name: 'due_date'},
                    { data: 'product_code', name: 'product_code'},
                    { data: 'cust_num', name: 'cust_num', className: 'wrapText'},
                    { data: 'shipping_zone', name: 'shipping_zone'},
                    { data: 'action', name: 'action', orderable: false, },
                ],
            });

            // Delete all pending data
            $(".cancel").on('click',function() {
                $.ajax({
                    url: "{{route('allocation.destroy')}}",
                    type: 'DELETE',
                    data: {
                        _token: "{{csrf_token()}}",
                    },
                    success: function(data) {
                        if (data == 'true') {
                            // Redirect to index page
                            location.href = "{{route('allocation.index')}}";
                        }
                    }
                });
            });

            // Delete checked pending data
            $("#delete").on('click',function(e) {
                // Get data of checked rows in DataTable
                var ids = [];
                var checked_row_data = 0;
                $('#pending').DataTable().$(".pending-row-data").each(function() {
                    if ($(this).prop('checked') == true) {
                        ids.push($(this).val());
                        checked_row_data++;
                    }
                });

                // Throw error if no data is checked
                if (checked_row_data == 0) {
                    Alert.warning('{{__('error.admin.atleastoneresource', ['resource' => __('admin.label.selected_order_lines')])}}');
                    return false;
                }

                e.preventDefault();
                let m = '{{__('admin.message.suredelete') }}';
                m = m.replace(':resource', '{{__('admin.title.orderLine') }}');
                Swal.fire({
                    title: 'Warning',
                    text: m,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes'
                }).then((result) => {
                    if (result.value) {
                        $.ajax({
                            url: "{{route('allocation.destroy')}}",
                            type: 'DELETE',
                            data: {
                                _token: "{{csrf_token()}}",
                                id: ids,
                            },
                            success: function(data) {
                                var show = document.querySelector('.msgsuccess');
                                show.style.display = "block";

                                $(".pending-row-all").prop('checked',false);
                                // Reload DataTable
                                // $('#pending').DataTable().draw(false); // set false to stay at current page; do this for serverside datatable
                                $('#pending').DataTable().ajax.reload( null, false ); // do this for for clientside datatable
                            },
                        });
                    }
                });
            });

            // Delete single pending data
            $('#pending').on('click','.delete',function(e) {
                var id = $(this).attr('id');

                e.preventDefault();
                let m = '{{__('admin.message.suredelete') }}';
                m = m.replace(':resource', '{{__('admin.title.orderLine') }}');
                Swal.fire({
                    title: 'Warning',
                    text: m,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes'
                }).then((result) => {
                    if (result.value) {
                        $.ajax({
                            url: "{{route('allocation.destroy')}}",
                            type: 'DELETE',
                            data: {
                                _token: '{{csrf_token()}}',
                                id: id,
                            },
                            success: function(data) {
                                var show = document.querySelector('.msgsuccess');
                                show.style.display = "block";

                                // Reload DataTable
                                // $('#pending').DataTable().draw(false); // set false to stay at current page; do th;is for serverside datatable
                                $('#pending').DataTable().ajax.reload( null, false ); // do this for for clientside datatable
                            },
                        });
                    }
                });
            });

            $(".save").on('click',function() {
                // Get data of checked rows in DataTable
                var allocation = {};
                var checked_row_data = 0;
                                            $("#generate_error").html("").addClass('hidden');

                $('#generate').DataTable().$(".generate-row-data").each(function() {
                    if ($(this).prop('checked') == true) {
                        // console.log($(this).data('qty_available'));
                        // allocation.push($(this).val());
                        allocation[$(this).val()]=$(this).data('qty_available');
                        checked_row_data++;
                    }
                });
                // console.log(allocation);
                // return;
// return;
                // Throw error if no data is checked
                if (checked_row_data == 0) {
                    Alert.warning('{{__('error.admin.atleastoneresource', ['resource' => __('admin.label.order_lines')])}}');
                    return false;
                }
                // console.log(allocation);
                $(".pageloader").css("display", "block");
                // Store selected to allocations
                $.ajax({
                    url: "{{ route('allocation.store') }}",
                    type: "POST",
                    data: {
                        allocation: allocation,
                        _token: "{{csrf_token()}}",
                    },
                    complete: function(data) {
                        $(".pageloader").css("display", "none");
                    },
                    success: function(data) {
                        if (data == 'true') {
                            // Alert Success
                            Swal.fire({
                                icon: 'success',
                                title: "{{__('success.Success') }}",
                                text: "{{__('success.added', ['resource' => __('admin.label.order_lines')])}}",
                                showConfirmButton: true,
                                showCloseButton: false,
                                timer: 1500
                            })

                            // // Reload DataTable;
                            // $('#pending').DataTable().draw(false); // set false to stay at current page - serverside true only
                            // $('#generate').DataTable().ajax.reload(null,false); // set null,false to stay at current page - serverside false only

                            // Reload datatable
                            $('#pending').DataTable().ajax.reload();
                            $('#generate').DataTable().ajax.reload();
                            $("#modal_filtered").modal('hide');
                        }
                        else {
                            $("#generate_error").html(data).removeClass('hidden');
                            // Alert.warning(data);
                        }
                    },
                });
            });

            // If header checkbox is checked, the whole checkbox in the datatable will be checked, and vice versa.
            $(".generate-row-all").on('click',function() {
                if ($(this).prop('checked') == true) {
                    $('#generate').DataTable().rows( { filter: 'applied' } ).every( function () {
                        var row = this.node();
                        $(this.node()).find('input[type="checkbox"][name="row_data[]"]').prop('checked', true);
                    });
                }
                else {
                    $('#generate').DataTable().$(".generate-row-data").each(function() {
                        $(this).prop('checked',false);
                    })
                }
            });
            $(".pending-row-all").on('click',function() {
                if ($(this).prop('checked') == true) {
                    $('#pending').DataTable().$(".pending-row-data").each(function() {
                        $(this).prop('checked',true);
                    })
                }
                else {
                    $('#pending').DataTable().$(".pending-row-data").each(function() {
                        $(this).prop('checked',false);
                    })
                }
            });

            // If one checkbox is unchecked in the datatable, set the header checkbox to unchecked
            $('#generate').on('click','.generate-row-data',function() {
                if ($(this).prop('checked') == false) {
                    $(".generate-row-all").prop('checked',false);
                }
            });
            $('#pending').on('click','.pending-row-data',function() {
                if ($(this).prop('checked') == false) {
                    $(".pending-row-all").prop('checked',false);
                }
            });

            // Reset form and remove validation's error message
            $('#btn-reset').on('click', function (e) {
                $("form")[0].reset();
                $("label[class='error']").remove();
            });

            // Generate Filtered Order Lines
            $('#filter').click(function(){
                unit_quantity_format = "{{$unit_quantity_format}}";
                                                            $("#generate_error").html("").addClass('hidden');

                var type = new Array();
                $("input[name='type[]']:checked").each(function() {
                    type.push($(this).val());
                });

                // Throw error if no order type is selected
                // if (type.length == 0) {
                //     Alert.warning('{{__('error.admin.atleastoneresource', ['resource' => __('admin.label.order_type')])}}');
                //     return false;
                // }

                // Throw error if Warehouse field is empty
                if (!$('#whse_num').val()) {
                    Alert.warning('{{__('error.admin.required', ['resource' => __('admin.label.whse_num').' field'])}}');
                    return false;
                }
                if ( $.fn.DataTable.isDataTable('#generate') ) {
                    $('#generate').DataTable().destroy();
                }

                $('#generate tbody').empty();
                $(".generate-row-all").prop('checked',false);

                var columnsDetails=[];
                if ($("input[name='type[]']:checked").val() == "Customer Order"){
                    columnsDetails= [
                        { data: 'isChecked', name: 'isChecked', class: 'checkbox', width: "1%", orderable: false },
                        { data: 'type', name: 'type', 'searchable':false},
                        { data: 'whse_num', name: 'whse_num'},
                        { data: 'ref_num', name: 'co_num'},
                        { data: 'suffix', name: 'suffix'},
                        { data: 'ref_line', name: 'co_line'},
                        { data: 'ref_release', name: 'co_rel'},
                        { data: 'item_num', name: 'item_num'},
                        { data: 'item_desc', name: 'item_desc' },
                        { data: 'qty_required', name: 'qty_required', className: 'text-right'},
                        { data: 'qty_available', name: 'qty_available',  className: 'text-right'},
                        { data: 'qty_shortage', name: 'qty_shortage', className: 'text-right','searchable':true},
                        { data: 'qty_shipped', name: 'qty_shipped',  className: 'text-right','searchable':true},
                        { data: 'qty_released', name: 'qty_released',  className: 'text-right', visible: false},
                        { data: 'uom', name: 'uom'},
                        { data: 'due_date', name: 'due_date'},
                        { data: 'product_code', name: 'product_code'},
                        { data: 'cust_num', name: 'cust_num'},
                        { data: 'shipping_zone', name: 'shipping_zone'},
                    ];
                }

                if ($("input[name='type[]']:checked").val() == "Job Order"){
                    columnsDetails= [
                        { data: 'isChecked', name: 'isChecked', class: 'checkbox', width: "1%", orderable: false },
                        { data: 'type', name: 'type', 'searchable':false},
                        { data: 'whse_num', name: 'whse_num'},
                        { data: 'ref_num', name: 'ref_num'},
                        { data: 'suffix', name: 'suffix'},
                        { data: 'ref_line', name: 'oper_num'},
                        { data: 'ref_release', name: 'sequence'},
                        { data: 'item_num', name: 'item_num'},
                        { data: 'item_desc', name: 'item_desc'},
                        { data: 'qty_required', name: 'qty_required', className: 'text-right','searchable':true},
                        { data: 'qty_available', name: 'qty_available',  className: 'text-right'},
                        { data: 'qty_shortage', name: 'qty_shortage', className: 'text-right','searchable':true},
                        { data: 'qty_shipped', name: 'qty_shipped',  className: 'text-right','searchable':true},
                        { data: 'qty_released', name: 'qty_released',  className: 'text-right', visible: false},
                        { data: 'uom', name: 'uom'},
                        { data: 'due_date', name: 'due_date'},
                        { data: 'product_code', name: 'product_code'},
                        { data: 'cust_num', name: 'cust_num'},
                        { data: 'shipping_zone', name: 'shipping_zone'},
                    ];
                }

                if ($("input[name='type[]']:checked").val() == "Transfer Order"){
                     columnsDetails= [
                        { data: 'isChecked', name: 'isChecked', class: 'checkbox', width: "1%", orderable: false },
                        { data: 'type', name: 'type', 'searchable':false},
                        { data: 'whse_num', name: 'whse_num'},
                        { data: 'ref_num', name: 'trn_num'},
                        { data: 'suffix', name: 'suffix'},
                        { data: 'ref_line', name: 'trn_line'},
                        { data: 'ref_release', name: 'ref_release',},
                        { data: 'item_num', name: 'item_num'},
                        { data: 'item_desc', name: 'item_desc' },
                        { data: 'qty_required', name: 'qty_required', className: 'text-right'},
                        { data: 'qty_available', name: 'qty_available',  className: 'text-right'},
                        { data: 'qty_shortage', name: 'qty_shortage', className: 'text-right','searchable':true},
                        { data: 'qty_shipped', name: 'qty_shipped',  className: 'text-right','searchable':true},
                        { data: 'qty_released', name: 'qty_released',  className: 'text-right', visible: false,'searchable':true},
                        { data: 'uom', name: 'uom'},
                        { data: 'due_date', name: 'due_date'},
                        { data: 'product_code', name: 'product_code'},
                        { data: 'cust_num', name: 'cust_num'},
                        { data: 'shipping_zone', name: 'shipping_zone'},
                    ];
                }


                $('#generate').DataTable({
                    scrollX: true,
                    destroy: true,
                    autoWidth: true,
                    processing: true,
                    serverSide: true,
                    responsive: true,
                    searching: true,
                    bSortCellsTop: true, // set ordering icon at the first thead tr
                    lengthMenu: [[10, 25], [10, 25]],
                    ordering: true,
                    order: [], // set to empty array to remove the icon of sorting on the first column
                    ajax: {
                        url : '{{ route('allocation.generate') }}?dev={{$_REQUEST["dev"]??""}}',
                        method: 'get',
                        data: {
                            whse_num: $('#whse_num').val(),
                            from_order_num: $('#from_order_num').val(),
                            to_order_num: $('#to_order_num').val(),
                            from_due_date: $('#from_due_date').val(),
                            to_due_date: $('#to_due_date').val(),
                            from_item_num: $('#from_item_num').val(),
                            to_item_num: $('#to_item_num').val(),
                            from_product_code: $('#from_product_code').val(),
                            to_product_code: $('#to_product_code').val(),
                            from_cust_num: $('#from_cust_num').val(),
                            to_cust_num: $('#to_cust_num').val(),
                            type: type,
                        },
                        dataSrc: function (json) {
                            var result = new Array();
                            for (i = 0; i < json.data.length; i++) {
                                // var checked_value = omit('item_desc',json.data[i])
                                var checked_value = json.data[i].id;
                                // var qty_shortage= json.data[i].qty_shortage;

                                // var qty_shortage= Math.max(json.data[i].qty_required-json.data[i].qty_available,0);
                                                                var qty_shortage= Math.max(parseFloat(json.data[i].qty_required)-parseFloat(json.data[i].qty_available),0);

                                var qty_shortage_class= qty_shortage>0?"text-danger":"";
                                // console.log(parseFloat(json.data[i].qty_required),parseFloat(json.data[i].qty_available));
                                // if(parseFloat(json.data[i].qty_required)<= parseFloat(json.data[i].qty_available)){
                                    // return;
                                // json.data[i].qty_shortage=qty_shortage;
                                result.push({
                                    'isChecked': "<input type='checkbox' class='generate-row-data' name='row_data[]' data-qty_available='"+parseFloat(json.data[i].qty_available)+"' value='"+JSON.stringify(checked_value)+"'>",
                                    'type'  : json.data[i].type,
                                    'whse_num' : json.data[i].whse_num,
                                    'ref_id' : json.data[i].ref_id,
                                    'ref_num' : json.data[i].ref_num,
                                    'suffix' : json.data[i].suffix,
                                    'ref_line' : json.data[i].ref_line,
                                    'ref_release' : json.data[i].ref_release,
                                    'item_num' : json.data[i].item_num,
                                    'item_desc' : json.data[i].item_desc,
                                    'qty_required': numberFormatPrecision(json.data[i].qty_required,unit_quantity_format),
                                    'qty_available': numberFormatPrecision(json.data[i].qty_available,unit_quantity_format),
                                    'qty_shortage' : "<div class='td-qty-shortage "+qty_shortage_class+"'>"+numberFormatPrecision(qty_shortage,unit_quantity_format)+"</div>",
                                    'qty_shipped': numberFormatPrecision(json.data[i].qty_shipped,unit_quantity_format),
                                    'qty_released': json.data[i].qty_released,
                                    'uom' : json.data[i].uom,
                                    'due_date' : json.data[i].due_date,
                                    'product_code' : json.data[i].product_code,
                                    'cust_num' : json.data[i].cust_num,
                                    'shipping_zone' : json.data[i].shipping_zone,
                                });
                            // }
                            }
                            return result;
                        }
                    },
                    columns:columnsDetails,
                    rowId: 'id',
                });

                // Set header checkbox to checked
                // $(".generate-row-all").prop('checked',true);
            });

            // Set Qty Shortage to red if it is more than 0
            // $('#generate,#pending').DataTable().on('draw',function() {
            //     $(".td-qty-shortage").each(function() {
            //         var qty_shortage = parseFloat($(this).text());
            //         if (qty_shortage > 0) {
            //             $(this).closest('td').css('background-color','red').css('color','white');
            //         }
            //     });
            // });


            $(window).resize(function (e) {
                $("#pending").DataTable().columns.adjust();
            });
        });

        jQuery(function($){
            $("#form_allocation").validate({
                onchange:true,
                rules:{
                    whse_num:{
                        remote:{
                            url: "{{ route('validation') }}",
                            type: "post",
                            data: { _token : $('input[name="_token"]').val() }
                        }
                    },
                    from_order_num:{
                        remote:{
                            url: "{{ route('validateOrderNum') }}",
                            type: "post",
                            data: { _token : $('input[name="_token"]').val() }
                        }
                    },
                    to_order_num:{
                        remote: {
                            url: "{{ route('validateOrderNum') }}",
                            type: "post",
                            data: { _token: $('input[name="_token"]').val() }
                        }
                    },
                    from_item_num:{
                        remote:{
                            url: "{{ route('validation') }}",
                            type: "post",
                            data: { _token : $('input[name="_token"]').val() }
                        }
                    },
                    to_item_num:{
                        remote: {
                            url: "{{ route('validation') }}",
                            type: "post",
                            data: { _token: $('input[name="_token"]').val() }
                        }
                    },
                    from_product_code:{
                        remote:{
                            url: "{{ route('validation') }}",
                            type: "post",
                            data: { _token : $('input[name="_token"]').val() }
                        }
                    },
                    to_product_code:{
                        remote: {
                            url: "{{ route('validation') }}",
                            type: "post",
                            data: { _token: $('input[name="_token"]').val() }
                        }
                    },
                    from_cust_num:{
                        remote:{
                            url: "{{ route('validation') }}",
                            type: "post",
                            data: { _token : $('input[name="_token"]').val() }
                        }
                    },
                    to_cust_num:{
                        remote: {
                            url: "{{ route('validation') }}",
                            type: "post",
                            data: { _token: $('input[name="_token"]').val() }
                        }
                    }
                },
                messages:{
                    whse_num:{
                        remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.whse_num') ]) }}"
                    },
                    from_order_num:{
                        remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.ref_num') ]) }}"
                    },
                    to_order_num:{
                        remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.ref_num') ]) }}"
                    },
                    from_item_num:{
                        remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                    },
                    to_item_num:{
                        remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                    },
                    from_product_code:{
                        remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.product_code') ]) }}"
                    },
                    to_product_code:{
                        remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.product_code') ]) }}"
                    },
                    from_cust_num:{
                        remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.cust_num') ]) }}"
                    },
                    to_cust_num:{
                        remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.cust_num') ]) }}"
                    },
                },
            });
        });

    </script>
    <head>
        <style>
        html body .content .content-wrapper{
            margin: 0.6rem 1.2rem;}
        .card{
            box-shadow: 0px 0px 0px transparent !important;
            border: 0 !important;}
        .card-header1{
            margin-bottom: -10px;}
        textarea{
            padding:2px;}
        form .form-actions{
            padding-bottom: 14px !important;
            border-bottom: 1px solid #dadada;}
        div.form-group>table>tbody>tr>td>input{
            padding:8px;
            height: 25px;}
        div.text-center{
            margin-bottom: -25px;
            padding-bottom: 15px !important;
            border-bottom: 1px solid #dadada;}
        .wrapText{
            word-break: break-all;}
        /* table header */
        .table th, .table td{
            padding: 0.2rem 0.2rem !important;}
        table.table.table-bordered.table-hover.nowrap.dataTable.no-footer>thead>tr:nth-child(1){
            background-color: rgb(220,220,220);}
        table#pending.table.table-bordered.table-hover.nowrap>thead>tr:first-child>th,
        table#generate.table.table-bordered.table-hover.nowrap>thead>tr:first-child>th,
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer thead>tr>th.checkbox.sorting_disabled,
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer thead>tr>th.sorting,
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer thead>tr>th.sorting_disabled,
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer thead>tr>th.sorting_asc,
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer thead>tr>th.dtcr_tableheader.sorting_desc,
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer thead>tr>th.text-center.sorting_disabled{
            background-color: white;}
        /* scrollable filter table */
         div#generate_wrapper table#generate.table.table-bordered.table-hover.nowrap.no-footer.dataTable{
            width:147% !important;}
        div.dataTables_scrollBody>table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer{
            width:150% !important;}
        /* checkbox & action button*/
        table.table.table-bordered.table-hover.nowrap.dataTable.no-footer>thead>tr:nth-child(2)>th:first-child,
        div#pending_wrapper table.table.table-bordered.table-hover.nowrap.dataTable.no-footer>thead>tr:nth-child(1)>th:last-child,
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer tbody>tr.odd>td:first-child,
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer tbody>tr.even>td:first-child,
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer tbody>tr.odd>td:last-child,
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer tbody>tr.even>td:last-child,
        table#generate.table.table-bordered.table-hover.nowrap.dataTable.no-footer tbody>tr>td:first-child{
            text-align: center;
            vertical-align: middle;}
        /* delete button */
        button.btn.btn-danger.delete{
            padding: 0.2rem 0.2rem !important;}
        /* table body */
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer tbody>tr.odd>td,
        table#pending.table.table-bordered.table-hover.nowrap.dataTable.no-footer tbody>tr.even>td{
            vertical-align: middle;}
        /* Hide the search of datatable located at its top right */
        #generate_filter, #pending_filter {
            display: none;}
        </style>
    </head>
    @include('util.selection')
    @include('util.datepicker')
@endsection
