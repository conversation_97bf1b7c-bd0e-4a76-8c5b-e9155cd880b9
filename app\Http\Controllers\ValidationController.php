<?php

namespace App\Http\Controllers;

use App\AlternateBarcode;
use App\Job;
use App\Loc;
use App\Item;
use App\CustomerOrderItem;
use App\LotLoc;
use App\Vendor;
use App\ItemLoc;
use App\Machine;
use App\PurchaseOrderItem;
use App\Customer;
use App\Employee;
use App\ItemWarehouse;
use App\JobMatl;
use App\JobRoute;
use App\Lot;
use App\StageLoc;
use App\Warehouse;
use App\matl_trans;
use App\ReasonCode;
use App\WorkCenter;
use App\ProductCode;
use App\PurchaseOrder;
use App\Rules\StrongPassword;
use App\Rules\ExistingPassword;
use App\TransferLine;
use App\TransferOrder;
use App\PicklistTestItems;
use App\PicklistSummaryDetails;
use App\ShippingZone;
use App\TransferOrderLinesSubline;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use LangleyFoxall\LaravelNISTPasswordRules\PasswordRules;
use Illuminate\Support\Facades\DB;
use App\UomConv;
use App\View\TparmView;
use App\User;
use Illuminate\Support\Facades\Session;
use App\GRNItem;
use App\GRN;
use App\CustomerReturn;

class ValidationController extends Controller
{

    public function frontEndValidate(Request $request)
    {
        $vinput = $request->except('_token');

        foreach ($vinput as $name => $value) {
            if ($name == "whse_num" || $name == "whse" || $name == 'from_whse_num' || $name == 'to_whse_num') {
                $model = new Warehouse();
                $model->whse_status = 1;
            }
            if ($name == "pick_num" || $name == "from_pick_num" || $name == "to_pick_num") {
                $model = new PicklistSummaryDetails();
            }
            if ($name == "item_num" || $name == "item" || $name == 'from_item_num' || $name == 'to_item_num') {
                $model = new Item();
            }
            if (strpos($name, 'item_num') !== false) {
                $model = new Item();
                $model->item_status = 1;
            }
            if (strpos($name, 'wc') !== false) {
                $model = new WorkCenter();
            }
            if ($name == "emp_num" || $name == 'from_emp_num' || $name == 'to_emp_num' || $name == 'emp_id') {
                $model = new Employee();
            }
            if ($name == "res_id" || $name == 'from_res_id' || $name == 'to_res_id' || $name == 'next_res_id') {
                $model = new Machine();
            }
            if ($name == "job_num" || $name == 'from_job_num' || $name == 'to_job_num') {
                $model = new Job();
            }
            if ($name == "oper_num" || $name == 'from_oper_num' || $name == 'to_oper_num') {
                $model = new JobRoute();
            }
            if ($name == "product_code" || $name == 'from_product_code' || $name == 'to_product_code') {
                $model = new ProductCode();
            }
            if ($name == "wc_num") {
                $model = new WorkCenter();
            }
            if ($name == "matl_code" || $name == 'from_matl_code' || $name == 'to_matl_code') {
                $model = new Item();
            }
            if ($name == "loc_num" || $name == "toLoc" || $name == "from_loc" || $name == "from_loc_num" || $name == "to_loc_num") {
                $model = new Loc();
            }
            if ($name == 'po_num' || $name == 'from_po_num' || $name == 'to_po_num' || $name == 'from_order_num' || $name == 'to_order_num') {
                $model = new PurchaseOrderItem();
            }
            if ($name == 'co_num' || $name == 'from_co_num' || $name == 'to_co_num') {
                $model = new CustomerOrderItem();
            }
            if ($name == 'trn_num' || $name == 'from_trn_num' || $name == 'to_trn_num') {
                $model = new TransferLine();
            }
            if ($name == 'from_vend_num' || $name == 'to_vend_num' || $name == 'vend_num') {
                $model = new Vendor();
            }
            if ($name == "cust_num" || $name == 'from_cust_num' || $name == 'to_cust_num') {
                $model = new Customer();
            }
            if ($name == "from_lot_num" || $name == 'to_lot_num' || $name == 'lot_num') {
                $model = new Lot();
            }
            if ($name == "from_shipping_zone_code" || $name == 'to_shipping_zone_code') {
                $model = new ShippingZone();
            }
            if ($name == "from_grn_num" || $name == 'to_grn_num') {
                $model = new GRN();
            }
            if ($name == 'reason_code') {
                $model = new ReasonCode();
                $result = $model->exists($value);
            } else {
                $result = $model->exists($value);
            }


            if ($result == true) {
                return "true";
            } else {
                if (AlternateBarcode::where('alternate_barcode', $value)->exists()) {
                    return "true";
                }
                return "false";
            }
        }
    }
    public function frontEndValidate2(Request $request)
    {
        $vinput = $request->except('_token');

        foreach ($vinput as $name => $value) {
            if ($name == "whse_num" || $name == "whse" || $name == 'from_whse_num' || $name == 'to_whse_num') {

                $obj = Warehouse::where('whse_num', $value)
                    ->where('site_id', auth()->user()->site_id)->first();

                if ($obj) {
                    if ($obj->whse_status) {
                        $result = true;
                    } else {
                        $result = __('error.mobile.inactive', ['resource' => __('mobile.label.whse_num'), "name" => $value]);
                    }
                } else {
                    $result = __('error.mobile.notexist3', ['resource' => __('mobile.label.whse_num'), 'resource2' => $value]);

                    // $result = __('error.mobile.notexist', ['resource' => __('mobile.label.whse_num')]);
                    // $result['result'] = false;
                    // $result['msg'] = __('error.mobile.notexist', ['resource' => $value]);
                }
                return json_encode($result);
            }
            if ($name == "pick_num" || $name == "from_pick_num" || $name == "to_pick_num") {
                $model = new PicklistSummaryDetails();
            }
            if ($name == "item_num" || $name == "item" || $name == 'from_item_num' || $name == 'to_item_num') {
                $model = new Item();
            }
            if (strpos($name, 'item_num') !== false) {
                $model = new Item();
                $model->item_status = 1;
            }
            if (strpos($name, 'wc') !== false) {
                $model = new WorkCenter();
            }
            if ($name == "emp_num" || $name == 'from_emp_num' || $name == 'to_emp_num' || $name == 'emp_id') {
                $model = new Employee();
            }
            if ($name == "res_id" || $name == 'from_res_id' || $name == 'to_res_id' || $name == 'next_res_id') {
                $model = new Machine();
            }
            if ($name == "job_num" || $name == 'from_job_num' || $name == 'to_job_num') {
                $model = new Job();
            }
            if ($name == "oper_num" || $name == 'from_oper_num' || $name == 'to_oper_num') {
                $model = new JobRoute();
            }
            if ($name == "product_code" || $name == 'from_product_code' || $name == 'to_product_code') {
                $model = new ProductCode();
            }
            if ($name == "wc_num") {
                $model = new WorkCenter();
            }
            if ($name == "matl_code" || $name == 'from_matl_code' || $name == 'to_matl_code') {
                $model = new Item();
            }
            if ($name == "loc_num" || $name == "toLoc" || $name == "from_loc" || $name == "from_loc_num" || $name == "to_loc_num") {
                $model = new Loc();
            }
            if ($name == 'po_num' || $name == 'from_po_num' || $name == 'to_po_num' || $name == 'from_order_num' || $name == 'to_order_num') {
                $model = new PurchaseOrderItem();
            }
            if ($name == 'co_num' || $name == 'from_co_num' || $name == 'to_co_num') {
                $model = new CustomerOrderItem();
            }
            if ($name == 'trn_num' || $name == 'from_trn_num' || $name == 'to_trn_num') {
                $model = new TransferLine();
            }
            if ($name == 'from_vend_num' || $name == 'to_vend_num' || $name == 'vend_num') {

                $obj = Vendor::where('vend_num', $value)
                    ->where('site_id', auth()->user()->site_id)->first();

                if ($obj) {
                    if ($obj->vend_status) {
                        $result = true;
                    } else {
                        $result = __('error.mobile.inactive', ['resource' => __('mobile.label.vend_num'), "name" => $value]);
                    }
                } else {
                    $result = __('error.mobile.notexist3', ['resource' => __('mobile.label.vend_num'), 'resource2' => $value]);
                }
                return json_encode($result);
            }
            if ($name == 'from_grn_num' || $name == 'to_grn_num' || $name == 'grn_num') {

                $obj = GRN::where('grn_num', $value)
                    ->where('site_id', auth()->user()->site_id)->first();

                if ($obj) {
                    if ($obj->grn_status == 'O') {
                        $result = true;
                    } else {
                        $result = __('error.mobile.resource_completed', ['resource' => __('mobile.label.grn_num'), "name" => $value]);
                    }
                } else {
                    $result = __('error.mobile.notexist3', ['resource' => __('mobile.label.grn_num'), 'resource2' => $value]);
                }
                return json_encode($result);
            }
            if ($name == "cust_num" || $name == 'from_cust_num' || $name == 'to_cust_num') {
                $model = new Customer();
            }
            if ($name == "from_lot_num" || $name == 'to_lot_num' || $name == 'lot_num') {
                $model = new Lot();
            }
            if ($name == "from_shipping_zone_code" || $name == 'to_shipping_zone_code') {
                $model = new ShippingZone();
            }
            if ($name == 'reason_code') {
                $model = new ReasonCode();
                $result = $model->exists($value);
            } else {
                $result = $model->exists($value);
            }


            if ($result == true) {
                return "true";
            } else {
                if (AlternateBarcode::where('alternate_barcode', $value)->exists()) {
                    return "true";
                }
                return "false";
            }
        }
    }

    public function JobReleasedValidation(Request $request)
    {
        if (Job::where('job_num', $request->job_num)->where('job_status', 'R')->exists()) {
            return "true";
        }
        return "false";
    }

    public function validateUser(Request $request)
    {

        try {
            if ($request->name) {
                $request->validate([
                    'name' =>
                    [
                        Rule::unique('users')
                            ->where(function ($query) {
                                $query->where('site_id', auth()->user()->site_id);
                            })
                    ]
                ]);
            } else if ($request->email) {
                $request->validate(
                    [
                        //                    'email'=>'email|max:255',
                        'email' => 'email:rfc,dns'
                    ]
                );
            } else if ($request->password) {
                $request->validate([
                    'password' => [new StrongPassword, PasswordRules::register($request->name), (new ExistingPassword)->setUserId($request->userId)],
                ]);
            }
        } catch (ValidationException $e) {
            // return $e->errors();

            // return error message only for "New password cannot be same as current password. Please enter a different password."
            // this is to display correct error message instead of jquery validation default message for remote validation
            $errors = $e->errors();
            if (array_key_exists("password", $errors)) {
                foreach ($errors["password"] as $error) {
                    if ($error == "New password cannot be same as current password. Please enter a different password.") {
                        return '"' . $error . '"';
                    }
                }
            }

            return "false";
        }

        if ($request->email) {
            $email = DB::table('users')->where('email', $request->email)->where('site_id', auth()->user()->site_id)->exists();

            if ($email) {
                return 'The email has already been taken.';
            }
        }


        return "true";
    }

    public function validateUserGroup(Request $request)
    {
        try {
            if ($request->name) {
                $request->validate([
                    'name' =>
                    [
                        Rule::unique('user_group_lists')
                            ->where(function ($query) {
                                $query->where('site_id', auth()->user()->site_id);
                            })
                    ]
                ]);
            }
        } catch (ValidationException $e) {
            return "false";
        }

        return "true";
    }

    public function validateVendDO(Request $request)
    {

        $vend = PurchaseOrder::where('vend_name', $request->vend_name)->where('vend_do', $request->vend_do)->where('po_num', $request->po_num)->exists();

        if ($vend == true) {
            return "true";
        } else
            return "false";
    }

    public function validateOrderNum(Request $request)
    {
        $request->except('_token');

        $job = new Job();
        if ($job->exists($request->from_order_num) || $job->exists($request->to_order_num)) {
            return "true";
        }
        $to = new TransferOrder();
        if ($to->exists($request->from_order_num) || $to->exists($request->to_order_num)) {
            return "true";
        }
        $co = new CustomerOrderItem();
        if ($co->exists($request->from_order_num) || $co->exists($request->to_order_num)) {
            return "true";
        } else
            return "false";
    }

    public function validateLoc(Request $request)
    {
        $whse_num = $request['whse_num'];
        if ($request['loc_num'])
            $loc_num = $request['loc_num'];
        if ($request['toLoc'])
            $loc_num = $request['toLoc'];
        if ($request['from_loc'])
            $loc_num = $request['from_loc'];
        if ($request['stage_num'])
            $loc_num = $request['stage_num'];

        $loc = new Loc;
        if ($loc->exists($loc_num, $whse_num)) {
            return "true";
        } else
            return "false";
    }

    public function validateLoc2(Request $request)
    {
        $loc_num = utf8_encode(htmlspecialchars_decode(base64_decode($request->loc_num)));
        $loc = Loc::where('loc_num', $loc_num)->first();
        if ($loc) {
            if ($loc->loc_status == 0) {
                return 'inactive';
            }
        } else {
            return 'not exist';
        }

        return "exists";
    }

    public function validateLoc3(Request $request)
    {
        $whse_num = $request['whse_num'];
        if ($request['loc_num'])
            $loc_num = $request['loc_num'];
        if ($request['toLoc'])
            $loc_num = $request['toLoc'];
        if ($request['from_loc'])
            $loc_num = $request['from_loc'];
        if ($request['stage_num'])
            $loc_num = $request['stage_num'];

        $loc = new Loc;
        $loc = $loc->where('loc_num', $loc_num)->where('whse_num', $whse_num)->exists();
        if ($loc) {
            return "true";
        } else
            return "false";
    }

    public function validateUserExists(Request $request)
    {
        $user = utf8_encode(htmlspecialchars_decode(base64_decode($request->user)));
        $user = User::where('name', $user)->first();
        if ($user) {
            if ($user->status != 'A') {
                return 'not active';
            }
            // else if ($user->status == 'L')
            // {
            //     return "locked";
            // }
            // else
            // {
            //     return 'not exist';
            // }
        } else {
            return 'not exist';
        }

        return "exists";
    }

    public function validateItemWhseLoc(Request $request)
    {
        $whse_num = $request['whse_num'];
        $item_num = $request['item_num'];
        $loc_num = $request['loc_num'];

        $loc = ItemLoc::where('whse_num', $whse_num)->where('item_num', $item_num)->where('loc_num', $loc_num)
            ->whereHas('location', function ($q) use ($whse_num) {
                $q->where('whse_num', $whse_num)->where('loc_status', 1);
            })->first();

        if ($loc) {
            return "true";
        } else
            return "false";
    }

    public function validateLocNotTransit(Request $request)
    {

        $whse_num = $request['whse_num'];

        if ($request['loc_num'])
            $loc_num = $request['loc_num'];

        if ($request['toLoc'])
            $loc_num = $request['toLoc'];

        $loc = Loc::where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('loc_type', '!=', 'T')->where('site_id', auth()->user()->site_id)->exists();

        if ($loc == "true") {
            return "true";
        } else {
            return "false";
        }
    }

    public function validateStageLoc(Request $request)
    {
        $whse_num = $request['whse_num'];
        $loc_num = '';
        if ($request['loc_num'])
            $loc_num = $request['loc_num'];
        if ($request['stage_num'])
            $loc_num = $request['stage_num'];

        $loc = new StageLoc();
        if ($loc->exists($loc_num, $whse_num)) {
            return "true";
        } else
            return "false";
    }

    public function validateWhseJob(Request $request)
    {
        $whse_num = $request['whse_num'];
        $job_num = $request['job_num'];

        $whse_job = new JobMatl();
        if ($whse_job->exists($whse_num, $job_num)) {
            return "true";
        } else
            return "false";
    }

    public function validateJobRoute(Request $request)
    {
        $job_num = $request['job_num'];
        $oper_num = $request['oper_num'];

        $job_route = new JobRoute;
        $job_route =  $job_route->where('oper_num', $oper_num)->where('job_num', $job_num)->first();

        if ($job_route) {
            return "true";
        } else
            return "false";
    }

    public function validateCoLine(Request $request)
    {
        $co_num = $request['co_num'];
        $co_line = $request['co_line'];

        $co_item = new CustomerOrderItem();
        if ($co_item->lineExists($co_num, $co_line)) {
            return "true";
        } else
            return "false";
    }

    public function validateCrLine(Request $request)
    {
        $return_num = $request['return_num'];
        $cr_line = $request['return_line'];

        $co_item = new CustomerOrderItem();
        if ($co_item->lineExists($return_num, $cr_line)) {
            return "true";
        } else
            return "false";
    }
    public function validateTrnLine(Request $request)
    {
        $trn_num = $request['trn_num'];
        $trn_line = $request['trn_line'];

        $transLine = new TransferLine();
        if ($transLine->lineExists($trn_num, $trn_line)) {
            return "true";
        } else
            return "false";
    }

    public function validatePoLine(Request $request)
    {
        $po_num = $request['po_num'];
        $po_line = $request['po_line'];

        $po_item = new PurchaseOrderItem();
        if ($po_item->lineExists($po_num, $po_line)) {
            return "true";
        } else
            return "false";
    }
    public function validatePoReturnLine(Request $request)
    {
        $po_num = $request['po_num'];
        $po_line = $request['po_line'];

        $po_item = new PurchaseOrderItem();
        if ($po_item->returnLineExists($po_num, $po_line)) {
            return "true";
        } else
            return "false";
    }
    public function validatePoItem(Request $request)
    {
        $po_num = $request['po_num'];
        $item_num = $request['item_num'];

        $po_item = new PurchaseOrderItem();
        if ($po_item->itemExists($item_num, $po_num)) {
            return "true";
        } else
            return "false";
    }

    public function validatePoRel(Request $request)
    {
        $po_num = $request['po_num'];
        $po_line = $request['po_line'];
        $po_rel = $request['po_rel'];
        $po_item = new PurchaseOrderItem();
        if ($po_item->relExists($po_num, $po_line, $po_rel)) {
            return "true";
        } else
            return "false";
    }

    public function validateCoRel(Request $request)
    {
        $co_num = $request['co_num'];
        $co_line = $request['co_line'];
        $co_rel = $request['co_rel'];
        $co_item = new CustomerOrderItem();
        if ($co_item->relExists($co_num, $co_line, $co_rel)) {
            return "true";
        } else
            return "false";
    }

    public function validateNewLot(Request $request)
    {
        $whse_num = $request['whse_num'];
        $item_num = $request['item_num'];
        $lot_num = $request['lot_num'];

        $lotLocation = new LotLoc();
        if ($lotLocation->exists($whse_num, $item_num, $lot_num)) {
            return "true";
        } else
            return "false";
    }

    public function validateItemLot(Request $request)
    {
        $item = Item::where('item_num', $request->item_num)->where('lot_tracked', 1)->exists();
        if ($item) {
            return "true";
        } else {
            return "false";
        }
    }
    public function validateExistingLotOpen(Request $request)
    {
        $whse_num = $request['whse_num'];
        $item_num = $request['item_num'];
        $loc_num = $request['loc_num'];
        $lot_num = $request['lot_num'];



        $lotLocation = \App\Lot::where('lot_num', $lot_num)->where('item_num', $item_num)->exists();

        if ($lotLocation)
            return "true";
        return "false";
    }
    public function validateExistingLot(Request $request)
    {
        $whse_num = $request['whse_num'];
        $item_num = $request['item_num'];
        $loc_num = $request['loc_num'];
        $lot_num = $request['lot_num'];

        $lotLocation = new LotLoc();
        if ($lotLocation->locexists($whse_num, $item_num, $loc_num, $lot_num)) {
            return "true";
        } else
            return "false";
    }

    public function validateExistingLotTOReceipt(Request $request)
    {
        $trn_num = $request['trn_num'];
        $trn_line = $request['trn_line'];
        $item_num = $request['item_num'];
        $trn_lot = $request['lot_num'];

        $lot = new TransferOrderLinesSubline();
        if ($lot->lotexists($trn_num, $trn_line, $item_num, $trn_lot)) {
            return "true";
        } else
            return "false";
    }
    public function validateExistingLotTOReceiptUnit(Request $request)
    {
        $trn_num = $request['trn_num'];
        $trn_line = $request['trn_line'];
        $item_num = $request['item_num'];
        $trn_lot = $request['lot_num'];

        $lot = new TransferOrderLinesSubline();
        if ($lot->lotexistsunit($trn_num, $trn_line, $item_num, $trn_lot)) {
            return "true";
        } else
            return "false";
    }

    public function validateExistingSuffixMobile(Request $request)
    {
        $job_num = $request['job_num'];
        $suffix = $request['suffix'];

        $suffixnum = new JobRoute();
        if ($suffixnum->suffixexists($job_num, $suffix)) {
            return "true";
        } else
            return "false";
    }

    public function validateFrozen(Request $request)
    {
        $whse_num = base64_decode($request['whse_num']);
        $item_num = base64_decode($request['item_num']);
        $loc_num = base64_decode($request['loc_num']);
        $itemLoc = new ItemLoc();
        $itemLoc = $itemLoc->where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('item_num', $item_num)->first();
        if ($itemLoc && $itemLoc->freeze == 'Y') {
            return "true";
        } else
            return "false";
        $model = new ItemLoc();
    }

    public function validateJobScrapReason(Request $request)
    {
        $reason_code = $request['reason_code'];
        $reason = new ReasonCode();
        if ($reason->where('reason_class', 'JobScrap')->where('reason_num', $reason_code)->exists()) {
            return "true";
        } else
            return "false";
    }
    public function validateWIPReverseReason(Request $request)
    {
        $reason_code = $request['reason_code'];
        $reason = new ReasonCode();
        if ($reason->where('reason_class', 'WIPReverse')->where('reason_num', $reason_code)->exists()) {
            return "true";
        } else
            return "false";
    }
    public function validateEndJobReason(Request $request)
    {
        $reason_code = $request['reason_code'];
        $reason = new ReasonCode();
        if ($reason->where('reason_class', 'EndJobRun')->where('reason_num', $reason_code)->exists()) {
            return "true";
        } else
            return "false";
    }

    public function validateWarehouseItem(Request $request)
    {

        $item = Item::where('item_status', 1)->where('item_num', $request->item_num)->first();
        if (!$item) {
            return "false";
        }
        $itemLoc = ItemLoc::where('whse_num', $request->whse_num)
            ->where('item_num', $request->item_num);

        if ($request->loc_num) {
            $itemLoc->where('loc_num', $request->loc_num);
        }

        $itemLoc = $itemLoc->exists();

        if ($itemLoc) {
            return "true";
        } else {
            if (AlternateBarcode::where('alternate_barcode', $request->item_num)->exists()) {
                return "true";
            }
            return "false";
        }
    }
    public function validateWarehouseItemV3(Request $request)
    {

        $result = false;

        if (!isset($request->whse_num) || !isset($request->item_num) || $request->item_num == "NON-INV") {
            $result = true;
        } else {

            $item = Item::where('item_num', $request->item_num)
                ->where('site_id', auth()->user()->site_id)->first();
            if ($item) {
                if ($item->item_status) {
                    $itemWhse = ItemWarehouse::where('whse_num', $request->whse_num)
                        ->where('item_num', $request->item_num)
                        ->where('site_id', auth()->user()->site_id)->first();
                    if ($itemWhse) {
                        $result = true;
                    } else {
                        $result = __('error.mobile.notexist', ['resource' => 'Item']);
                    }
                } else {

                    $result = __('error.mobile.inactive', ['resource' => 'Item', 'name' => $request->item_num]);
                }
            } else {
                $result = __('error.mobile.notexist3', ['resource' => 'Item', 'resource2' => $request->item_num]);

                // $result = __('error.mobile.notexist', ['resource' => 'Item']);
            }
        }

        return json_encode($result);
    }

    public function validateOrderItem(Request $request)
    {


        $item_num = $request->item_num;
        $whse_num = $request->whse_num;
        $user = auth()->user();
        //        dd($user);
        //        $item = Item::where('item_num', $item_num)
        //                ->where('site_id', $user->site_id);
        //                ->where('item_status', 1);
        $params = $request->except(['type', '_token']);

        if ($request->type == "po_receipt") {

            $model = new PurchaseOrderItem();
            //            $item = $item->whereHas('po_items', function ($q) use ($po_num, $whse_num, $item_num) {
            //                $q->where('po_num', $po_num)
            //                        ->where('whse_num', $whse_num)
            //                        ->inventory()
            //                        ->open()
            //                        ->where('qty_ordered', '>', 0);
            //            });
        }
        if ($request->type == "co_return" || $request->type == "pink_n_ship" || $request->type == "co_pick" || $request->type == "co_unpick") {


            $model = new CustomerOrderItem();
            //            dd($params);
        }
        if ($request->type == "to_receipt" || $request->type == "to_ship") {
            $model = new TransferLine();
        }
        if ($request->type == "job_material" || $request->type == "job_material_return") {
            $model = new JobMatl();
        }

        //
        // dd($params);
        foreach ($params as $key => $param) {
            if ($key == "from_matl_item" || $key == "to_matl_item") {
                $key="matl_item";
            }
            if ($key == "whse_num") {
                if ($request->type == "job_material" || $request->type == "job_material_return") {
                    $model = $model->whereHas('job', function ($q) use ($whse_num) {
                        $q->where('whse_num', $whse_num);
                    });
                } else {
                    $model = $model->where($key, 'LIKE', $param);
                }
            } else {
                $model = $model->where($key, 'LIKE', $param);
            }
        }

        if ($request->type == "co_return" || $request->type == "co_pick" || $request->type == "co_unpick") {
            $model = $model->where('rel_status', 'O');
        }

        $model = $model->where('site_id', $user->site_id);
        $model = $model->first();
        if ($model) {
            return "true";
        }
        return "false";
    }

    public function validateGetItemfromItemWhse(Request $request)
    {

        $strItem = addCslashes($request->item_num, '\\');
        //$item_num_from_itemloc = ItemLoc::where('whse_num', $request->whse_num)->pluck('item_num')->toArray();
        $item_nums = ItemWarehouse::where('whse_num', $request->whse_num)->pluck('item_num')->toArray();
        // $item_num = Item::select('item_num')->where('item_num', 'LIKE', '%' . $strItem . '%')->value('item_num');
        $item_num = Item::select('item_num')->where('item_num', $strItem)->value('item_num');

        // $item_nums = array_unique(array_merge($item_num_from_itemloc, $item_num_from_itemwhse));

        //dd($request->item_num ,$item_num);
        if (in_array($item_num, $item_nums)) {
            return "true";
        } else {
            if (AlternateBarcode::where('alternate_barcode', $request->item_num)->exists()) {
                return "true";
            }
            return "false";
        }
    }








    public function validateGetItemfromItemLocItemWhse(Request $request)
    {

        $strItem = addCslashes($request->item_num, '\\');
        $item_num_from_itemloc = ItemLoc::where('whse_num', $request->whse_num)->pluck('item_num')->toArray();
        $item_num_from_itemwhse = ItemWarehouse::where('whse_num', $request->whse_num)->pluck('item_num')->toArray();
        // $item_num = Item::select('item_num')->where('item_num', 'LIKE', '%' . $strItem . '%')->value('item_num');
        $item_num = Item::select('item_num')->where('item_num', $strItem)->value('item_num');

        $item_nums = array_unique(array_merge($item_num_from_itemloc, $item_num_from_itemwhse));

        //dd($request->item_num ,$item_num);
        if (in_array($item_num, $item_nums)) {
            return "true";
        } else {
            if (AlternateBarcode::where('alternate_barcode', $request->item_num)->exists()) {
                return "true";
            }
            return "false";
        }
    }

    public function validatePOItemWhse(Request $request)
    {

        $po_item = PurchaseOrderItem::where('whse_num', $request->whse_num)
            ->where('po_num', $request->po_num)
            ->where('item_num', $request->item_num);

        $po_item = $po_item->exists();

        if ($po_item) {
            return "true";
        } else {
            if (AlternateBarcode::where('alternate_barcode', $request->item_num)->exists()) {
                return "true";
            }
            return "false";
        }
    }
    public function validatePoReturnQty(Request $request)
    {

        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $allow_over_return = $tparm->getTparmValue('POReturn', 'allow_over_return');
        $list = new ItemLoc();

        $whse_num = $request->whse_num;
        $loc_num = $request->loc_num;
        $item_num = $request->item_num;
        $qty_required = $request->qty_returnable;
        $qty_required_uom = $request->ref_uom;
        $uom_to = $request->uom;
        $vend_num = $request->vend_num;

        // dd();
        // dd($item_num, $whse_num, $loc_num, $qty_required, $qty_required_uom, $uom_to, $cust_num, $vend_num);
        $qty = $list->select('qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')
            ->where('item_num', $item_num)
            ->where('whse_num', $whse_num)
            ->where('loc_num', $loc_num)
            ->first();

        $qty['base_uom'] = $qty->uom;
        $qty['uom'] = $qty->uom;
        $qty['qty_available'] = $qty->qty_available;
        $qty['qty_available_conv'] = $qty->qty_available;
        $qty['qty_on_hand'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format, '.', '');
        $qty['qty_on_hand_conv'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format, '.', '');

        // Convert qty required to base uom and check which one is smaller. item loc qty on hand or co qty required.
        $convertUom = UomConv::convertUOM($qty->uom, $qty_required_uom, $qty_required_uom, $qty_required, $item_num, "", '', __('mobile.nav.co_picking'));
        $qtyReqConvert = $convertUom['conv_qty_to_base']['qty'];

        if ($allow_over_return) {
            $smallestValue = max($qty->qty_available, $qtyReqConvert);
        } else {
            $smallestValue = min($qty->qty_available, $qtyReqConvert);
        }

        if ($smallestValue == $qty->qty_available) {
            if ($uom_to == $qty->uom) {
                $qty['max_qty_input'] = $qty->qty_available;
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            } else {
                $convertUom = UomConv::convertUOM($qty->uom, $qty->uom, $uom_to, $smallestValue, $item_num, "", '', __('mobile.nav.co_picking'));
                $qty['max_qty_input'] = $convertUom['conv_qty_to_line']['qty'];
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            }
        } else if ($smallestValue == $qtyReqConvert) {
            if ($uom_to == $qty_required_uom) {
                $qty['max_qty_input'] = $qty_required;
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            } else {
                $convertUom = UomConv::convertUOM($qty->uom, $uom_to, $qty_required_uom, $smallestValue, $item_num, "", '', __('mobile.nav.co_picking'));
                $qty['max_qty_input'] = $convertUom['conv_qty_to_line']['qty'];
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty_required_uom;
            }
        }

        if ($uom_to) {
            $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, "", $vend_num, $uom_to);
            $qty['qty_available_conv'] = $uom_conv['qty'];
        }
        // dd($qty);
        return $qty;
        return 1;
    }


    public function reasonCodeValidation(Request $request)
    {

        $reason_code = ReasonCode::where('reason_class', 'POReturn')
            ->where('reason_num', $request->reason_code)
            ->exists();

        if ($reason_code) {
            return "true";
        } else {
            return "false";
        }
    }
    public function reasonValidation(Request $request)
    {

        $reason_code = ReasonCode::where('reason_class', $request->reason_class)
            ->where('reason_num', $request->reason_num)
            ->exists();


        if ($reason_code) {
            return "true";
        } else {
            return "false";
        }
    }

    public function validateCoNum(Request $request)
    {
        $vinput = $request->except('_token');

        foreach ($vinput as $name => $value) {

            if ($name == 'from_po_num' || $name == 'to_po_num' || $name == 'from_order_num' || $name == 'to_order_num') {
                $model = new CustomerOrderItem();
            }
            if ($name == 'co_num' || $name == 'from_co_num' || $name == 'to_co_num') {
                $model = new CustomerOrderItem();
            }

            $result = $model->exists($value);

            if ($result == true) {
                return "true";
            } else {

                return "false";
            }
        }
    }

    public function validateUomConv(Request $request)
    {
        $vinput = $request->except('_token');
        $item_num = $request->item_num;
        $selected_uom = $request->uom;

        $base_uom = Item::where('item_num', $item_num)->select('uom')->first();
        $item_uom_conv = UomConv::select('uom_from', 'uom_to', 'conv_type')
            ->where(function ($q) use ($base_uom) {
                $q->where('uom_to', $base_uom->uom)
                    ->orWhere('uom_from', $base_uom->uom);
            })
            ->where('conv_type', 'I')
            ->where('item_num', $item_num)
            ->get();
        $uom_conv = UomConv::select('uom_from', 'uom_to', 'conv_type')
            ->where(function ($q) use ($base_uom) {
                $q->where('uom_to', $base_uom->uom)
                    ->orWhere('uom_from', $base_uom->uom);
            })
            ->where('conv_type', 'G')
            ->get();
        $item_uom_from = $item_uom_conv->pluck('uom_from')->all();
        $item_uom_to = $item_uom_conv->pluck('uom_to')->all();
        $uom_from = $uom_conv->pluck('uom_from')->all();
        $uom_to = $uom_conv->pluck('uom_to')->all();
        $uom = collect($base_uom)->concat($uom_to)->concat($uom_from)->concat($item_uom_from)->concat($item_uom_to)->unique()->toArray();

        if (in_array($selected_uom, $uom)) {
            return "true";
        } else {
            return "false";
        }
    }

    public static function validateConvUOM(Request $request)
    {
        $result = ['result' => true, 'msg' => ''];

        $item_num = $request['item_num'];
        $cust_num = $request['cust_num'];
        $vend_num = $request['vend_num'];
        $base_uom = $request['base_uom'];
        $line_uom = $request['line_uom'];
        $selected_uom = $request['selected_uom'];

        if ($item_num == "NON-INV") {
            $base_uom = $line_uom;
        }

        if (empty($base_uom)) {
            $base_uom = Item::where('item_num', $item_num)->first()->uom;
        }



        // Selected UOM should be able to directly convert to Base UOM and Line UOMs
        $uom_conv = UomConv::where(function ($q) use ($cust_num, $vend_num, $item_num) {
            $q->where('conv_type', 'G');

            if (!empty($item_num)) {
                $q->orWhere(function ($query) use ($item_num) {
                    $query->where('conv_type', 'I')->where('item_num', $item_num);
                });

                if (!empty($cust_num)) {
                    $q->orWhere(function ($query) use ($item_num, $cust_num) {
                        $query->where('conv_type', 'C')->where('item_num', $item_num)->where('cust_num', $cust_num);
                    });
                }

                if (!empty($vend_num)) {
                    $q->orWhere(function ($query) use ($item_num, $vend_num) {
                        $query->where('conv_type', 'V')->where('item_num', $item_num)->where('vend_num', $vend_num);
                    });
                }
            }
        });

        $selected_to_base = clone $uom_conv;
        if ($base_uom != $selected_uom) {
            $selected_to_base = $selected_to_base->where(function ($q) use ($base_uom, $selected_uom) {
                $q->where(function ($query) use ($base_uom, $selected_uom) {
                    $query->where('uom_from', $base_uom)->where('uom_to', $selected_uom);
                })
                    ->orWhere(function ($query) use ($base_uom, $selected_uom) {
                        $query->where('uom_from', $selected_uom)->where('uom_to', $base_uom);
                    });
            })->exists();
        } else {
            $selected_to_base = true;
        }

        $selected_to_line = clone $uom_conv;
        if ($line_uom && $line_uom != $selected_uom) {
            $selected_to_line = $selected_to_line->where(function ($q) use ($line_uom, $selected_uom) {
                $q->where(function ($query) use ($line_uom, $selected_uom) {
                    $query->where('uom_from', $line_uom)->where('uom_to', $selected_uom);
                })
                    ->orWhere(function ($query) use ($line_uom, $selected_uom) {
                        $query->where('uom_from', $selected_uom)->where('uom_to', $line_uom);
                    });
            })->exists();
        } else {
            $selected_to_line = true;
        }

        if (!$selected_to_base) {
            $result = ['result' => false, 'msg' => __('error.mobile.uom_conv_not_exists', ['resource1' => $base_uom, 'resource2' => $selected_uom])];
        }

        if (!$selected_to_line) {
            $result = ['result' => false, 'msg' => __('error.mobile.uom_conv_not_exists', ['resource1' => $line_uom, 'resource2' => $selected_uom])];
        }

        return $result;
    }

    public function validateWarehouseItemv2(Request $request)
    {
        $result = ['result' => true, 'msg' => ''];

        if (!isset($request->whse_num) || !isset($request->item_num) || $request->item_num == "NON-INV") {
            $result['result'] = true;
        } else {
            $itemWhse = ItemWarehouse::where('whse_num', $request->whse_num)->where('item_num', $request->item_num)->where('site_id', auth()->user()->site_id);

            if ($itemWhse->exists()) {
                if ($itemWhse->first()->item->item_status) {
                    $result['result'] = true;
                } else {
                    $result['result'] = false;
                    $result['msg'] = __('error.mobile.inactive', ['resource' => 'Item', 'name' => $request->item_num]);
                }
            } else {
                $result['result'] = false;
                $result['msg'] = __('error.mobile.notexist', ['resource' => 'Item']);
            }
        }

        return $result;
    }
    public function inventoryCountValidation(Request $request)
    {
        $vinput = $request->except('_token');

        // dd($request->scan);
        if ($request->scan) {
            $item = new Item();
            // $model = $model->where('item_status', 1);
            $item = $item->where('item_status', 1);
            $item = $item->where('item_num', $request->item_num)->first();
            if (!$item || $request->item_num == "") {
                return "false";
            }
            $loc = new Loc();
            $loc = $loc->where('loc_status', 1);
            $loc = $loc->where('loc_num', $request->loc_num)->first();
            if (!$loc || $request->loc_num == "") {
                return "false";
            }
            if ($item->lot_tracked) {
                if ($request->lot_num == "") {
                    return "false";
                } else {
                    $lot = new Lot();
                    $lot = $lot->where('lot_num', $request->lot_num)->first();
                    if (!$lot) {
                        return "false";
                    }
                }
            }

            return "true";
        }

        foreach ($vinput as $name => $value) {
            if ($name == "whse_num" || $name == "whse" || $name == 'from_whse_num' || $name == 'to_whse_num') {
                $model = new Warehouse();
            }

            if ($name == "item_num" || $name == "item" || $name == 'from_item_num' || $name == 'to_item_num') {
                $model = new Item();
                // $model = $model->where('item_status', 1);
                $model = $model->where('item_status', 1);
                $model = $model->where('item_num', $request->item_num)->first();
                // dd($model);
            }



            if ($name == "loc_num" || $name == "toLoc" || $name == "from_loc" || $name == "from_loc_num" || $name == "to_loc_num") {
                $model = new Loc();
                $model = $model->where('loc_status', 1);
                $model = $model->where('loc_num', $request->loc_num)->first();
            }

            if ($name == "from_lot_num" || $name == 'to_lot_num' || $name == 'lot_num') {

                $model = new Lot();
                $model = $model->where('lot_num', $request->lot_num)->first();

                // $model = $model->where('lot_status', 1);

            }


            // $result = $model->exists($value);
            if ($model) {
                return "true";
            } else {

                return "false";
            }
        }
    }
    public static function checkTransitPickingLocValidtion($request, $inputName, $disable_create_new_item_location = true, $required = true, $transit = true, $picking = true)
    {
        // dd($request, $inputName);
        $transtype =  Session::get('modulename', 'MiscReceipt');
        $loc_num = $request[$inputName];
        if ($transtype == "MiscReceipt") {
            $label = $inputName == "toLoc" ? "To Loc" : "Loc";
        } else {
            $label = $inputName == ("toLoc" || "to_loc") ? "To Loc" : "From Loc";
        }

        if (isset($request['to_whse']) && ($inputName == "toLoc" || $inputName == "to_loc")) {
            $whse_num = $request['to_whse'];
        } else {
            $whse_num = $request['whse_num'];
        }
        $item_num = $request['item_num'];

        if ($loc_num == "" && $required) {
            // dd($loc_num);
            $result = __('error.mobile.required', ['resource' => 'This field']);
            return $result;
        } else {
            $obj = Loc::where('whse_num', $whse_num)->where('loc_num', $loc_num)->first();

            if ($obj) {
                if (!$obj->loc_status) {
                    $result = __('error.mobile.inactive', ['resource' => $label, "name" => $loc_num]);
                    return $result;
                } elseif ($obj->loc_type == "T" && $transit) {
                    $result = __('error.mobile.validate_transit');
                    return $result;
                } elseif ($obj->pick_locs == 1 && $picking) {

                    $result = __('error.mobile.validate_picking');
                    return $result;
                }
            } else {
                if ($disable_create_new_item_location) {
                    $result = __('error.mobile.notexist3', ['resource' => $label, 'resource2' => $loc_num]);
                    return $result;
                } else {
                    $loc = new Loc;
                    $loc->whse_num = $whse_num;
                    $loc->loc_num = $loc_num;
                    $loc->loc_type = "S";
                    $loc->loc_status = 1;
                    $loc->save();
                }

                // $result['result'] = false;
                // $result['msg'] = __('error.mobile.notexist', ['resource' => $value]);
            }
        }
        // Skip the validation for ItemLoc for MiscRecipt and TOShipping
        // $arrSkippingCheckItemLoc = array( 'MiscReceipt'=>1, 'TOShipping'=>2);


        // if(!array_key_exists($request['transModule'],$arrSkippingCheckItemLoc)){
        $item_loc = new ItemLoc();
        $obj = $item_loc->where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('item_num', $item_num)->where('site_id', auth()->user()->site_id)->first();
        // dd($obj);
        if ($obj) {
            if ($obj->freeze == 'Y') {
                // dd("a");
                // $label = $label . " [" . $loc_num . "]";
                $result = __('error.mobile.loc_freeze', ['resource' => $item_num, 'resource2' => $loc_num]);
                return $result;
            }
        }
        // else {
        //    return __('error.admin.notexistin', ['resource1' => __('admin.label.item_num'), 'name' => $loc_num, 'resource2' => __('admin.label.loc_num')]);
        // }
        // }
        // $label = $label . " [" . $loc_num . "]";
        // $result = __('error.mobile.loc_freeze', ['resource' => "", 'resource2' => $label]);
        // return $result;
        return true;
    }
    public static function checkLotNumValidtion($request, $required = true)
    {
        // dd($request, $inputName);
        $item = $lot_num = $request['lot_num'];

        if ($item == "" && $required) {
            $result = __('error.mobile.required', ['resource' => 'This field']);
            return $result;
        } else {
            $obj = Lot::where('item_num', $request['item_num'])->where('lot_num', $item)->first();

            if (!$obj) {
                //     if (!$obj->lot_status) {
                //         $result = __('error.mobile.inactive', ['resource' => __('mobile.label.lot_num'), "name" => $item]);
                //         return $result;
                //     }
                // } else {
                $result = __('error.mobile.notexist3', ['resource' => __('mobile.label.lot_num'), 'resource2' => $lot_num]);

                // $result = __('error.mobile.notexist', ['resource' => __('mobile.label.lot_num')]);
                return $result;
                // $result['result'] = false;
                // $result['msg'] = __('error.mobile.notexist', ['resource' => $value]);
            }
        }


        return true;
    }
    public static function checkWhseValidation($whse_num, $required = true)
    {
        if ($whse_num == "" && $required) {
            $result = __('error.mobile.required', ['resource' => 'This field']);
            return $result;
        } else {
            $obj = Warehouse::where('whse_num', $whse_num)
                ->where('site_id', auth()->user()->site_id)->first();

            if ($obj) {
                if (!$obj->whse_status) {
                    $result = __('error.mobile.inactive', ['resource' => __('mobile.label.whse_num'), "name" => $whse_num]);
                    return $result;
                }
            } else {
                $result = __('error.mobile.notexist3', ['resource' => __('mobile.label.whse_num'), 'resource2' => $whse_num]);

                // $result = __('error.mobile.notexist', ['resource' => __('mobile.label.whse_num')]);
                return $result;
                // $result['result'] = false;
                // $result['msg'] = __('error.mobile.notexist', ['resource' => $value]);
            }
        }
        return true;
    }
    public static function checkItemNumValidation($item_num, $whse_num = "", $required = true)
    {
        if ($item_num == "" && $required) {
            $result = __('error.mobile.required', ['resource' => 'This field']);
            return $result;
        } else {
            $item = Item::where('item_num', $item_num)
                ->where('site_id', auth()->user()->site_id)->first();
            if ($item) {
                if ($item->item_status) {
                    if ($whse_num != "") {
                        $itemWhse = ItemWarehouse::where('whse_num', $whse_num)
                            ->where('item_num', $item_num)
                            ->where('site_id', auth()->user()->site_id)->first();
                        if (!$itemWhse) {
                            $result = __('error.mobile.notexist', ['resource' => 'Item']);
                            return $result;
                        }
                    }
                } else {

                    $result = __('error.mobile.inactive', ['resource' => 'Item', 'name' => $item_num]);
                    return $result;
                }
            } else {
                $result = __('error.mobile.notexist3', ['resource' => "Item", 'resource2' => $item_num]);

                // $result = __('error.mobile.notexist', ['resource' => 'Item']);
                return $result;
            }
        }
        return true;
    }
    public static function checkLotLocQtyValidtion($request)
    {
        $qty = $request['qty'];
        $whse_num = $request['whse_num'];
        $item_num = $request['item_num'];
        $loc_num = $request['loc_num'];
        // dd($request->all());
        $item_loc = new ItemLoc();
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        // $qty  = 289861163.56;
        if ($qty == "" && $required) {
            $result = __('error.mobile.required', ['resource' => 'This field']);
            return $result;
        } else {
            $obj = $item_loc->where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('item_num', $item_num)->where('site_id', auth()->user()->site_id)->first();
            // dd($obj);
            if ($obj) {
                $qty_on_hand = $obj->qty_available;
                // dd($qty_on_hand,$qty);
                $converTedQty = UomConv::convertUOM(
                    $request->uom,
                    $request->base_uom,
                    $request->base_uom,
                    $qty_on_hand,
                    $item_num,
                    "",
                    '',
                    __('mobile.nav.stock_move')
                );
                $qty_on_hand = $converTedQty['conv_qty_to_base']['qty'];
                // dd($qty_on_hand, $qty);
                if ((float)$qty_on_hand < (float)$qty) {
                    // dd("a");
                    $qty_on_hand = numberFormatPrecision($qty_on_hand, $unit_quantity_format, '.', '');



                    $result = __('error.admin.lessthan_equalto', ['resource' => "Qty", 'value' => $qty_on_hand]);
                    // throw ValidationException::withMessages(['details' => 'Item ' . $item_num . ' at ' . $loc_num . ' is frozen.']);
                    return $result;
                }
            }
        }
        return true;
    }
    public static function checkJobNumValidation($job_num, $suffix)
    {
        // Send error if job_num's job_status is not released
        $checkJobNum = Job::where('job_num', $job_num)->where('suffix', $suffix)->first(); //->where('job_status', '!=', "R")->exists();

        // Verifying Job exist
        if (!$checkJobNum) {
            $result = __('error.mobile.notexist', ['resource1' => $job_num . '-' . $request->suffix]);
            // throw ValidationException::withMessages(['details' => 'Item ' . $item_num . ' at ' . $loc_num . ' is frozen.']);
            return $result;
        }
        if ($checkJobNum->job_status != "R") {
            return 'Job Order-' . $job_num . ' cannot be proceed due to status is not released.';
        }
        return true;
    }

    public static function checkJobRouteValidation($request)
    {
        $checkJobNum = JobRoute::with('job')->where('job_num', $request->job_num)->where('suffix', $request->suffix)->where('oper_num', $request->oper_num)->first();

        // Verifying Job exist
        if (!$checkJobNum) {
            $result = __('error.mobile.notexist', ['resource1' => '[' . $request->job_num . '-' . $request->suffix . '-' . $request->oper_num . ']']);
            return $result;

            // throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->job_num . '-' . $request->suffix . '-' . $request->oper_num . ']'])]);
        }
        return true;
    }

    public static function checkTOLineValidation($request)
    {
        $transOrder = TransferOrder::where('trn_num', $request->trn_num)->first();
        // Verifying Transfer Order exist
        if (!$transOrder) {
            $result = __('error.mobile.notexist', ['resource' => '[' . $request->trn_num . ']']);
            return $result;
        } else {
            // Verifying Tranfer Order status
            if ($transOrder->status == "C") {
                $result = 'TO-' . __('error.mobile.status_is_completed', ['resource' => $request->trn_num]);
                return $result;
            }
        }

        $transLine = TransferLine::where('trn_num', $request->trn_num)->where('trn_line', $request->trn_line)->first();
        // Verifying Tranfer Line exist
        if (!$transLine) {
            $result = __('error.mobile.notexist', ['resource' => '[' . $request->trn_num . '-' . $request->trn_line . ']']);
            return $result;
        } else {
            // Verifying Tranfer Line status
            if ($transLine->line_stat == "C") {
                $result = 'TO-' . __('error.mobile.status_is_completed', ['resource' => $request->trn_num]);
                return $result;
            }
        }
        return true;
    }

    public static function checkPOLineValidation($request)
    {
        $poLine = PurchaseOrderItem::where('po_num', $request->ref_num)->where('po_rel', $request->ref_release)->where('po_line', $request->ref_line)->first();
        // Verifying PO Line exist
        if (!$poLine) {
            $result = __('error.mobile.notexist', ['resource' => '[' . $request->ref_num . '-' . $request->ref_line . ']']);
            return $result;
        }

        return true;
    }

    public static function checkGRNValidation($request)
    {
        $grnitem = GRNItem::where('grn_num', $request->grn_num)->where('grn_line', $request->grn_line)->first();
        // Verifying PO Line exist
        if (!$grnitem) {
            $result = __('error.mobile.notexist', ['resource' => '[' . $request->grn_num . '-' . $request->grn_line . ']']);
            return $result;
        }

        if ($grnitem->status == 'C') {
            $result =__('error.mobile.resource_completed', ['resource' => __('mobile.label.grn')]);
            return $result;
        }

        return true;
    }

    // to be remove, function is already applied to the controller
    public static function stockMoveValidation($request)
    {

        $tparm = new TparmView();
        $disable_create_new_item_location = $tparm->getTparmValue('StockMove', 'disable_create_new_item_location');
        $errors = [];
        // $request['whse_num'] = "assasa";

        if ($request['whse_num']) {
            $whse_num = $request['whse_num'];
            $result = self::checkWhseValidation($whse_num);
            // dd($result);
            if ($result !== true) {
                // dd("a");
                $errors['whse_num'] = $result;
            }
        }
        if ($request['item_num']) {
            $item_num = $request['item_num'];
            $result = self::checkItemNumValidation($item_num, $request['whse_num']);

            if ($result !== true) {
                $errors['item_num'] = $result;
            }
        }
        if ($request['loc_num']) {
            $result = self::checkTransitPickingLocValidtion($request, 'loc_num', true);

            if ($result !== true) {
                $errors['loc_num'] = $result;
            }
        }
        if ($request['toLoc']) {
            // dd($request['toLoc']);
            $result = self::checkTransitPickingLocValidtion($request, 'toLoc', $disable_create_new_item_location);
            if ($result !== true) {
                $errors['toLoc'] = $result;
            }
        }
        if ($request['lot_num'] && $request['lot_tracked'] == 1) {
            $result = self::checkLotNumValidtion($request);
            if ($result !== true) {
                $errors['lot_num'] = $result;
            }
        }
        if ($request['qty']) {
            $result = self::checkLotLocQtyValidtion($request);
            if ($result !== true) {
                $errors['qty'] = $result;
            }
        }

        // dd($errors);
        return $errors;
    }

    public function validateReferenceNo(Request $request)
    {
        $reference_no = "";
        if ($request->from_reference_no) {
            $reference_no = $request->from_reference_no;
        } else {
            $reference_no = $request->to_reference_no;
        }

        $is_exists = Lot::where('reference_no', $reference_no)->count();

        if ($is_exists > 0) {
            return "true";
        } else {
            return "false";
        }
    }

    public function validateCoReturnNum(Request $request)
    {
        $return_num = $request->return_num;

        $is_exists = CustomerReturn::where('return_num', $return_num)->count();

        if ($is_exists > 0) {
            return "true";
        } else {
            return "false";
        }
    }

    public function getLotNumDef(Request $request)
    {
        $item_num = $request->item_num;

        if (!$item_num) {
            return "false";
        }

        $item = Item::where('item_num', $item_num)->first();

        if (!$item || !$item->lot_number_definition)
        {
            return "false";
        }

        $lot_num_def = $item->lot_number_definition;

        if ($request->last_defined_lot) {
            $next_lot_num = (intval(substr($request->last_defined_lot, -1 * $lot_num_def->running_number_digits)) + 1);
            $next_lot_num = sprintf('%0'. $lot_num_def->running_number_digits .'d', ($next_lot_num));
            return substr($request->last_defined_lot, 0, strlen($request->last_defined_lot) - $lot_num_def->running_number_digits) . $next_lot_num;
        }

        $next_lot_num = (intval(substr($lot_num_def->last_running_number_display, -1 * $lot_num_def->running_number_digits)) + 1);
        $next_lot_num = sprintf('%0'. $lot_num_def->running_number_digits .'d', ($next_lot_num));

        $lot_num_def_segment = str_replace("[[!year!]]", date('y'), $lot_num_def->lot_num_def_segment);
        $lot_num_def_segment = str_replace("[[!fullyear!]]", date('Y'), $lot_num_def_segment);
        $lot_num_def_segment = str_replace("[[!month!]]", date('m'), $lot_num_def_segment);
        $lot_num_def_segment = str_replace("[[!day!]]", date('d'), $lot_num_def_segment);

        $lot_num_def = $lot_num_def_segment . $next_lot_num;
        return $lot_num_def;
    }
}
