<?php

namespace App\Http\Controllers\Outgoing;

use App\Services\SapCallService;
use App\Services\SapApiCallService;
use App\SAPb1\SAPClient;
use App\SAPb1\Filters\Equal;
use App\SAPb1\Service;
use App\SAPb1\Config;
use App\Services\CallHttpService;
use App\SiteSetting;
use Alert;
use App\Location;
use App\AlternateBarcode;
use App\ItemLoc;
use App\TransferLine;
use App\TransferOrder;
use App\View\TparmView;
use App\SapTransferOrders;
use Illuminate\Http\Request;
use App\Services\LotService;
use App\Container;
use App\ItemWarehouse;
use App\UomConv;
use App\Item;
use App\Services\PalletService;
use DB;
use App\ContainerItem;
use App\TransferOrderLinesSubline;
use App\Services\UOMService;
use Illuminate\Support\Carbon;
use App\Services\GeneralService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\BarcodeController;
use Illuminate\Validation\ValidationException;
use App\Services\SiteConnectionService;
use App\SiteConnection;
use App\Http\Controllers\ValidationController;
use App\Services\MatltransService;
use App\Services\PreassignLotsService;

class TransferOrderShippingController extends Controller
{
    use \App\Traits\HasDefaultLoc;
    protected $generalService;

    public function __construct(GeneralService $generalService)
    {
        // $this->middleware('can:hasTOShip');
        $this->generalService = $generalService;
    }

    public function index()
    {
        if (!\Gate::allows('hasTOShip')) {
            return view('errors.404')->with('page', 'error');;
        }
        $checkPlan = ['AX-MT-STR-M', 'AX-MT-STR-A', 'AX-MT-FREE-M'];
        $plan = SiteSetting::select('plan_code')->where('site_id', auth()->user()->site_id)->first();
        if (in_array($plan->plan_code, $checkPlan)) {
            return view('errors.404v2')->with('page', 'error');
        }
        $tparm = new TparmView();
        $tparm = $tparm->getTparmValue('TOShipping', 'enable_warehouse');

        $lpnDef = PalletService::getDefaultLpnTransaction('TO Shipping');

        return view('shipping.toship.index')->with('tparm', $tparm)->with('lpnDef', $lpnDef);
    }

    public function showTOShipping(Request $request)
    {
        //dd($request);
        if (!\Gate::allows('hasTOShip')) {
            return view('errors.404')->with('page', 'error');;
        }
        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $allow_over_ship = $tparm->getTparmValue('TOShipping', 'allow_over_ship');

        // check to exist
        $checkTo = TransferOrder::where('trn_num', $request->trn_num)->where('from_whse', $request->whse_num)->where('site_id', auth()->user()->site_id)->exists();
        $checkWhse = TransferOrder::where('trn_num', $request->trn_num)->where('site_id', auth()->user()->site_id)->first();

        if (!$checkTo) {
            if ($checkWhse->from_whse != $request->whse_num) {
                //throw ValidationException::withMessages(['co_num' => 'TO-' . $request->trn_num. ' not matching with ' . $request->whse_num]);
                throw ValidationException::withMessages(['trn_num' => 'TO [' . $request->trn_num . '] does not match Whse [' . $request->whse_num . ']']);
            } else {
                throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => $request->trn_num])]);
            }
        }

        $trn_loc = TransferOrder::where('trn_num', $request->trn_num)->where('from_whse', $request->whse_num)->where('site_id', auth()->user()->site_id)->value('trn_loc');

        $TO_list = TransferLine::where('trn_num', $request->trn_num)->where('from_whse', $request->whse_num)->where('site_id', auth()->user()->site_id)->where('line_stat', 'O');
        $TO_item = TransferLine::where('trn_num', $request->trn_num)->where('from_whse', $request->whse_num);
        //Alternate Barcode Search
        if ($request->item_num != "") {
            $TO_list = $TO_list->where('item_num', 'like', '' . $request->item_num . '');
            $TO_item = $TO_item->where('item_num', $request->item_num);
        }
        if ($request->trn_line != "") {
            $TO_list = $TO_list->where('trn_line', $request->trn_line);
        }
        $TO_list = $TO_list->orderByRaw('cast(trn_line as unsigned) ASC')->get();
        $TO_item = $TO_item->first();
        //dd($TO_list->co)unt();
        $barCodeName = array();
        foreach ($TO_list as $list) {
            $altBarCode = AlternateBarcode::where('item_num', $list->item_num)->where('site_id', auth()->user()->site_id)->get();
            if ($altBarCode) {
                foreach ($altBarCode as $barCode) {

                    array_push($barCodeName, $barCode->alternate_barcode);
                }

                $list->setAttribute('altBarCode', $barCodeName);
            } else {
                $list->setAttribute('altBarCode', null);
            }
            $barCodeName = [];
        }

        // Send error if lot_num is expired and allow_expired_item = 0
        LotService::checkExpiryDate('allow_expired_item_TOShipping', $request);

        $transDate = Carbon::now()->toDateTimeString();
        Session::put('timestamp', $transDate);

        // Redirect to the Process screen.
        if ($request->item_num != null && $TO_list) {

            if ($TO_list->count() == 1) {
                $request->trn_num = $TO_item->trn_num;
                $request->trn_line = $TO_item->trn_line;
                $request->item_num = $TO_item->item_num;
                return ($this->process($request));
            }
        }

        // dd($trn_loc);

        if ($request->pick_by == 'pallet') {
            return view('shipping.toship.newtoshippalletlist')->with('TO_list', $TO_list)
                ->with('trn_num', $request->trn_num)
                ->with('trn_line', $request->trn_line)
                ->with('to_whse', $TO_item->to_whse)
                ->with('whse_num', $request->whse_num)
                ->with('item_num', $request->item_num)
                ->with('cust_num', $request->cust_num)
                ->with('trn_loc', $trn_loc)
                ->with('allow_over_ship', $allow_over_ship)
                ->with('unit_quantity_format', $unit_quantity_format);;
        }

        return view('shipping.toship.toshiplist')->with('TO_list', $TO_list)
            ->with('trn_num', $request->trn_num)
            ->with('trn_line', $request->trn_line)
            ->with('whse_num', $request->whse_num)
            ->with('item_num', $request->item_num)
            ->with('allow_over_ship', $allow_over_ship)
            ->with('unit_quantity_format', $unit_quantity_format);
    }

    public function backTOShipping(Request $request)
    {
        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $allow_over_ship = $tparm->getTparmValue('TOShipping', 'allow_over_ship');
        // $TO_list = TransferLine::where('trn_num', $request->trn_num)->where('from_whse', $request->whse_num)->where('item_num', 'like', '%'. $request->item_num.'%')->where('site_id', auth()->user()->site_id)->get();
        $TO_list = TransferLine::where('trn_num', $request->trn_num)->where('from_whse', $request->whse_num)->where('site_id', auth()->user()->site_id);
        // $TO_item = TransferLine::where('trn_num', $request->trn_num)->where('item_num', $request->item_num)->where('from_whse', $request->whse_num)->first();
        $TO_item = TransferLine::where('trn_num', $request->trn_num)->where('item_num', $request->item_num)->where('from_whse', $request->whse_num);
        $TO_list = $TO_list->orderByRaw('cast(trn_line as unsigned) ASC');
        if ($request->item_num != "") {
            $TO_list = $TO_list->where('item_num', 'like', '' . $request->item_num . '');
            $TO_item = $TO_item->where('item_num', $request->item_num);
        }
        $TO_list = $TO_list->get();
        $TO_item = $TO_item->first();

        // Send error if lot_num is expired and allow_expired_item = 0
        LotService::checkExpiryDate('allow_expired_item_TOShipping', $request);

        $transDate = Carbon::now()->toDateTimeString();
        Session::put('timestamp', $transDate);

        // Redirect to the Process screen.
        if ($request->item_num != null && $TO_list) {

            if ($TO_list->count() == 1) {
                $request->trn_num = $TO_item->trn_num;
                $request->trn_line = $TO_item->trn_line;
                $request->item_num = $TO_item->item_num;
                return ($this->process($request));
            }
        }
        $trn_line = "";
        return view('shipping.toship.toshiplist')->with('TO_list', $TO_list)
            ->with('trn_num', $request->trn_num)
            ->with('trn_line', $trn_line)
            ->with('whse_num', $request->whse_num)
            ->with('item_num', $request->item_num)
            ->with('allow_over_ship', $allow_over_ship)
            ->with('unit_quantity_format', $unit_quantity_format);
    }

    public function process(Request $request)
    {
        //dd($request);
        $tparm = new TparmView;
        $enable_warehouse = $tparm->getTparmValue('TOShipping', 'enable_warehouse');
        $disable_lot_number_selection = $tparm->getTparmValue('TOShipping', 'disable_lot_number_selection');
        $allow_over_ship = $tparm->getTparmValue('TOShipping', 'allow_over_ship');
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        // $sap_trans_order_integration =

        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $sap_consolited_batch_snyc = $tparm->getTparmValue('TOShipping', 'enable_consolidated_shipping');
        $batch_id = generateBatchId("TOShipping");

        //$sap_consolited_batch_snyc =1;
        // dd($sap_consolited_batch_snyc);
        $to = TransferLine::with('item')
            ->where('trn_num', $request->trn_num)
            ->where('trn_line', $request->trn_line)
            ->first();
        $last_doc_num = "";
        if ($sap_consolited_batch_snyc == 1) {
            // Get the Doc Num

            $last_doc_num = DB::table('sap_to_batch_sync')
                ->where('trn_num', $request->trn_num)
                ->where('sync_status', 1)
                ->where('TO_type', 1)
                ->orderBy('id', 'desc')
                ->where('site_id', auth()->user()->site_id)
                ->pluck('doc_num')
                ->first();
        }

        $list = new TransferLine();
        $trndetails = $list->with('item')->where('trn_num', $request->trn_num)->where('trn_line', $request->trn_line)->where('from_whse', $request->whse_num)->where('site_id', auth()->user()->site_id)->first();
        // dd($trndetails);

        if (@$trndetails->qty_required < $trndetails->qty_shipped) {
            $trndetails->qty_required = $trndetails->qty_shipped;
        }

        if ($trndetails) {
            $trndetails['item_desc'] = $trndetails->item->item_desc ?? null;
            $trndetails['lot_tracked'] = $trndetails->item->lot_tracked ?? null;
            $trndetails['to_uom'] = $trndetails->uom;
            $trndetails['qty_required'] = $trndetails->qty_required - $trndetails->qty_shipped;
            $trndetails['qty_required_conv'] = $trndetails['qty_required'];

            $defaults = $this->getLocByRankIssue($trndetails->from_whse, $trndetails->item_num);

            $def = $this->getDefaultLocLotQtyELNS($to, $to->item, $disable_lot_number_selection);

            $trndetails['qty_on_hand'] = $defaults['qty_available'];
            $trndetails['qty_on_hand_conv'] = $defaults['qty_available'];

            $trndetails['qty_available'] = $defaults['qty_available'];
            $trndetails['qty_available_conv'] = $defaults['qty_available'];

            if ($trndetails['lot_num'] == null && $def == null) {
                $trndetails['lot_num'] = "";
            } else if ($trndetails['lot_num'] == null) {
                $trndetails['lot_num'] = $def['lot_num'];
            } else {
                $trndetails['lot_num'] = $trndetails['lot_num'];
            }

            $trndetails['loc_num'] = $defaults['loc_num'];
            $trndetails['base_uom'] = $trndetails->item->uom ?? null;
        } else {
            $trndetails['item_desc'] = "";
            $trndetails['lot_tracked'] = "";
            $trndetails['to_uom'] = "";
            $trndetails['qty_required'] = "";
            $trndetails['qty_required_conv'] = "";
            $trndetails['qty_on_hand'] = "";
            $trndetails['qty_on_hand_conv'] = "";

            $trndetails['qty_available'] = "";
            $trndetails['qty_available_conv'] = "";

            $trndetails['lot_num'] = "";
            $trndetails['loc_num'] = "";
            $trndetails['base_uom'] = "";
        }
        $TO_list = TransferLine::where('trn_num', $request->trn_num)->where('from_whse', $request->whse_num)->where('site_id', auth()->user()->site_id)->where('line_stat', 'O');

        if ($TO_list->count() > 1) {
            //$concel_href =
            $back_href = redirect()->route('backTOShippingList', ['whse_num' => $request->whse_num, 'trn_num' => $request->trn_num, 'item_num' => '']);
        } else {
            $back_href = redirect()->route('TransferOrderShipping');
        }
        // dd($back_href);

        return view('shipping.toship.process', compact('disable_lot_number_selection'))
            ->with('to_ship', $trndetails)
            ->with('defaults', $defaults)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('enable_warehouse', $enable_warehouse)
            ->with('sap_trans_order_integration', $sap_trans_order_integration)
            ->with('sap_consolited_batch_snyc', $sap_consolited_batch_snyc)
            ->with('last_doc_num', $last_doc_num)
            ->with('back_href', $back_href->getTargetUrl())
            ->with('allow_over_ship', $allow_over_ship)
            ->with('batch_id', $batch_id);
    }

    /**
     * Ship transfer order
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\View\View
     * @throws ValidationException
     */
    public function shipTransferOrder(Request $request)
    {
        if (!\Gate::allows('hasTOShip')) {
            return view('errors.404')->with('page', 'error');
        }

        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        $errors = [];
        $request = validateSansentiveValue($request);

        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_require_check_online == 1 && $sap_trans_order_integration == 1) {
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id, null, 'TO Ship', 1);

            if ($checkConnection > 2) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }
        }

        $validateErrors = self::toShipValidation($request->all());

        if (!empty($validateErrors[0])) {
            $errors = $validateErrors[0];
            return back()->withErrors($errors)->withInput();
        } else {
            $arrJsonEncodeParameters = json_encode($request->except('_token'));

            DB::beginTransaction();
            try {

                $toLoc = TransferOrder::select('trn_loc')->where('trn_num', $request->trn_num)->value('trn_loc');
                /*
                // $transLine = new TransferLine();
                // $transLine = $transLine->where('trn_num', $request->trn_num)->where('trn_line', $request->trn_line)->first();

                // // Verifying TOLine exist
                // if (!$transLine) {
                //     throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->trn_num . '-' . $request->trn_line . ']'])]);
                // }

                // Rewrite the ReadOnly fields
                // $request['trn_num'] = $transLine->trn_num;
                // $request['trn_line'] = $transLine->trn_line;
                // $request['whse_num'] = $transLine->from_whse;
                // $request['item_num'] = $transLine->item_num;
                // //$request['from_whse'] = $transLine->from_whse;
                // //$request['to_whse'] = $transLine->to_whse;

                // $transferorder = new TransferOrder();
                // $transferorder = $transferorder->where('trn_num', $request->trn_num)->where('site_id', auth()->user()->site_id)->first();
                // $request['from_whse'] = $transferorder->from_whse;
                // $request['to_whse'] = $transferorder->to_whse;

                //dd($request);



                // $toLoc = TransferOrder::select('trn_loc')->where('trn_num', $request->trn_num)->value('trn_loc');
                // $request->merge([
                //     'to_loc' => $toLoc
                // ]);

                // $request = validateSansentiveValue($request);
                // dd($request);
                // $transferorder = new TransferOrder;
                // // Throw error if TO has no transit location
                // $checkTransitLoc = $transferorder->where('trn_num', $request->trn_num)->whereIn('trn_loc', ['', null])->exists();
                // if ($checkTransitLoc) {
                //     throw ValidationException::withMessages([__('error.mobile.no_transit_loc', ['resource' => $request->trn_num])]);
                // }

                // $checkLocType = Location::select('loc_type')->where('loc_num', $request->loc_num)->value('loc_type');
                // if ($checkLocType == "T") {
                //     throw ValidationException::withMessages([__('error.mobile.transit_loc_ship', ['resource' => $request->loc_num])]);
                // }

                // dd($request,$checkTransitLoc);
                // $request->validate([
                //     'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
                //     'from_whse' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
                //     'to_whse' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
                //     'loc_num' => 'required|exists:locs,loc_num,loc_status,1,site_id,' . auth()->user()->site_id,
                // ], [
                //     'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
                //     'from_whse.exists' =>  __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
                //     'to_whse.exists' =>  __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
                //     'loc_num.exists' =>  __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
                // ]);

                // // Send error if trn_num's status is not open (Focus on header)
                // $checkTrnNum = $transferorder->where('trn_num', $request->trn_num)->where('status', '!=', "C")->first();
                // if (!$checkTrnNum) {
                //     throw ValidationException::withMessages(['trn_num' => 'TO-' . $request->trn_num . ' cannot be proceed due to status is completed/closed']);
                // }

                // Send error if trn_num && trn_line is completed (Focus on line)
                // $checTrnNumLine = TransferLine::where('trn_num', $request->trn_num)->where('trn_line', $request->trn_line)->first();
                // if ($checTrnNumLine) {
                //     if ($checTrnNumLine->line_stat == "C") {
                //         throw ValidationException::withMessages(['trn_num' => 'TO-' . $request->trn_num . ' cannot be proceed due to status is completed/closed']);
                //     }
                // }
                */
                // Send error if lot_num is expired and allow_expired_item = 0
                LotService::checkExpiryDate('allow_expired_item_TOShipping', $request);

                $transDate = Carbon::now()->toDateTimeString();
                Session::put('timestamp', $transDate);

                $checkTrnNum = TransferOrder::where('trn_num', $request->trn_num)->where('status', '!=', "C")->first();
                Session::put('transferOrderShipping', $checkTrnNum);

                // $convertUom = MatltransService::convertItemUom($request->item_num, $request->qty_to_ship, $request->uom, null, null, __('mobile.nav.to_receipt'));
                // $request['qty_conv'] = $convertUom['qty'];
                // $request['uom_conv'] = $convertUom['uom'];


                $executeTransferOrderShip = $this->generalService->executeTransferOrderShip($request);


                if (!$executeTransferOrderShip) {
                    //Alert::error('Error','');
                    Alert::error('Error', __('error.processed', ['process' => __('Transfer Order Shipping')]));
                    return $this->index();
                }

                // Store in New Table
                $tparm = new TparmView;
                $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
                $check_enable_toship = $tparm->getTparmValue('TOShipping', 'enable_consolidated_shipping');
                $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');

                //Future webhook here

                if ($sap_trans_order_integration == 1) {
                    if ($check_enable_toship == 1) {
                        if ($request->document_num != null) {
                            // Checking
                            $getCheckTrn_num =  DB::table('sap_to_batch_sync')->select('trn_num')->where('site_id', auth()->user()->site_id)->where('doc_num', $request->document_num)->value('trn_num');

                            if ($getCheckTrn_num != null) {
                                if ($getCheckTrn_num != $request->trn_num) {
                                    throw ValidationException::withMessages(['trn_num' => 'TO-' . $request->trn_num . ' cannot be proceed due to Doc Num ' . $request->document_num . ' already used by other TO num.']);
                                    //DB::rollback();
                                }
                            }
                        }
                    }
                    //dd('Abc'.$request->trn_num,$getCheckTrn_num);
                    $erp_ID = TransferLine::select('erp_ID')->where('trn_num', $request->trn_num)
                        ->where('trn_line', $request->trn_line)
                        ->where('site_id', auth()->user()->site_id)
                        ->value('erp_ID');
                    $arrJsonEncodeParameters = json_encode($request->except('_token'));
                    DB::table('sap_to_batch_sync')->insert([
                        'trn_num' => $request->trn_num,
                        'trn_line' => $request->trn_line,
                        'erp_ID' => $erp_ID,
                        'from_whse' => $request->whse_num,
                        'to_whse' => $request->to_whse,
                        'loc_num' => $request->loc_num,
                        'lot_num' => $request->lot_num,
                        'item_num' => $request->item_num,
                        'TO_type' => 1,
                        'qty_shipped' => $request->qty_to_ship,
                        'qty_shipped_uom' => $request->uom,
                        'doc_num' => $request->document_num,
                        'sync_status' => 1,
                        //'status' => 0,
                        'site_id' => auth()->user()->site_id,
                        'created_by' => auth()->user()->name,
                        'modified_by' => auth()->user()->name,
                        'created_date' => now(),
                        'modified_date' => now(),
                        'json_parameters' => $arrJsonEncodeParameters
                    ]);

                    $recordship = array(
                        'to_num' => $request->trn_num,
                        'line' => $request->trn_line,
                        'uniqekey' => md5($request->trn_num . $request->trn_line . $toLoc . $request->lot_num . $request->item_num . auth()->user()->site_id),
                        'frm_whse'  => $request->from_whse,
                        'qty'       => $request->qty_to_ship,
                        'to_whse'   => $request->to_whse,
                        'frm_loc'   => $request->loc_num,
                        'doc_num'    => $request->document_num,
                        'ship_status' => 0,
                        'TO_type'    => 1,
                    );
                    SapTransferOrders::create($recordship);

                    $from = "to-ship";
                    // $res = SapCallService::postTOShippingInventoryTransfer($request);


                    if ($request->last_ship == 'Yes' && $request->document_num != NULL) {
                        if ($sap_single_bin == 1) {
                            $result = SapApiCallService::postTOShippingInventoryTransferBulk($request, 'TO Ship Bulk');
                        } else {
                            $result = SapCallService::postTOShippingInventoryTransferBulk($request, 'TO Ship Bulk');
                        }
                        if ($result != 200) {
                            Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');

                            //DB::rollback();

                            // $data   = Session::get('data');
                            // //$result = Session::get('results');
                            // $extras = Session::get('sap_service');
                            // $user = auth()->user();

                            // $id =  DB::table('integration_logs')->insertGetId([
                            //     'trans_num' => 0,
                            //     'site_id' => auth()->user()->site_id,
                            //     'type' => 'sap',
                            //     'method' => 'post',
                            //     'process_name' => 'Bulk TO Ship',
                            //     'post_from' => 'CoShippingController_ShipCo()',
                            //     'post_data' => json_encode($data),
                            //     'response' => $result,
                            //     'extras' =>  json_encode(['sap_service'=>$extras]),
                            //     'status' => 2,
                            //     'created_by' => $user ? $user->name : "System",
                            //     'created_date' => now(),
                            //     'modified_date' => now(),
                            // ]);



                            // Session::forget('data');
                            // Session::forget('results');
                            // Session::forget('sap_service');

                            // return app('App\Http\Controllers\RouteController')->BackButton();
                        } else {
                            Alert::success('Success', __('success.processed', ['process' => __('Transfer Order Shipping')]));
                        }
                    } else {

                        Alert::success('Success', __('success.processed', ['process' => __('Transfer Order Shipping')]));
                    }
                    if ($check_enable_toship == 0) {
                        if ($sap_single_bin == 1) {
                            $result = SiteConnectionService::postIntergrationTrans('TO Ship', $request);
                            //dd($result,"lalalal");
                        } else {
                            if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                                $result = SapCallService::postStockTransferFromMaltrans('TO Ship', $request);
                            } else {





                                if (config('icapt.enable_sap_resync')) {
                                    $result = SapCallService::postTOShippingInventoryTransferResync($request);
                                } else {
                                    $result = SapCallService::postTOShippingInventoryTransfer($request);
                                }
                            }
                        }
                        if ($result != 200) {
                            Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');

                            //DB::rollback();

                            //return redirect()->route('backTOShippingList', ['whse_num' => $request->from_whse, 'trn_num' => $request->trn_num, 'item_num' => '']);
                        } else {
                            Alert::success('Success', __('success.processed', ['process' => __('Transfer Order Shipping')]));
                        }
                    }
                } else {

                    Alert::success('Success', __('success.processed', ['process' => __('Transfer Order Shipping')]));
                }
            } catch (\Exception $e) {
                DB::rollback();
                throw $e;
            }

            // update preassign lots
            // $uom_conv = UomConv::convert($request->uom, $request->qty_to_ship, $request->item_num, null, null, null);
            // PreassignLotsService::updatePreassignLot('to', $request->trn_num, $request->trn_line, $request->item_num, $request->lot_num, auth()->user()->site_id, (-1 * $uom_conv['qty']));

            DB::commit();
        }

        if (! count($errors)) {
            $transDate = Carbon::now()->toDateTimeString();
            Session::put('timestamp', $transDate);
            Session::put('modulename', 'TOShip');

            Session::put('trn_num', $request->trn_num);
            Session::put('whse_num', $request->whse_num);
            Session::put('item_num', $request->item_num);

            // Generate barcode
            $transType = 'TranOrderShipping';

            if ($request->lot_num != null) {
                $check_expiry_date = LotService::getExpiryDate($request);

                // Generate barcode
                $input = BarcodeController::GetTOShippingLabelData($request->from_whse, $request->to_whse, $request->trn_num, $request->trn_line, $request->item_num, $request->item_desc, $request->loc_num, $request->lot_num, $request->qty, $request->uom, $check_expiry_date, $transDate, $transType, null);
            } else {
                // Generate barcode
                $input = BarcodeController::GetTOShippingLabelData($request->from_whse, $request->to_whse, $request->trn_num, $request->trn_line, $request->item_num, $request->item_desc, $request->loc_num, $request->lot_num, $request->qty, $request->uom, null, $transDate, $transType, null);
            }

            $tparm = new TparmView;
            $print_label = $tparm->getTparmValue('TOShipping', 'print_label');

            if ($print_label == 1) {
                return BarcodeController::showLabelDefinition($input);
            } else {
                return back();
                // redirect()->route('backTOShippingList', ['whse_num' => $request->from_whse, 'trn_num' => $request->trn_num, 'item_num' => '']);
                // return app('App\Http\Controllers\RouteController')->BackButton();
            }
        } else {
            return back()->withErrors($errors)->withInput();
            // return redirect()->route('backTOShippingList', ['whse_num' => $request->from_whse, 'trn_num' => $request->trn_num, 'item_num' => '']);
        }
    }

    public function getTransItemLoc($item, $whse, $loc = "")
    {
        $list = new ItemLoc();
        $itemLoc = $list->select('loc_num')->where('item_num', $item)->where('whse_num', $whse)->where('loc_num', 'like', '%' . $loc . '%')
            ->get();

        return $itemLoc;
    }

    public function checkExpiryDateTOShipping(Request $request)
    {
        // Send error if lot_num is expired
        return LotService::checkExpiryDate('allow_expired_item_TOShipping', $request);
    }

    //Ship TO by pallet process
    public function shipTOByPallet(Request $request)
    {
        if (!\Gate::allows('hasTOShip')) {
            return view('errors.404')->with('page', 'error');;
        }
        // dd($request);
        // Send error if lpn_num's is not exist
        $checkLpn = Container::where('lpn_num', $request->lpn_num_field)->exists();
        if (!$checkLpn) {
            throw ValidationException::withMessages(['lpn_num' => 'LPN-' . $request->lpn_num_field . ' is not exist.']);
        }

        // Get Pallet Loc
        DB::beginTransaction();
        try {
            $lpnNum = $request->lpn_num_field;
            $getPalletLoc = Container::where('lpn_num', $request->lpn_num_field)->first();
            for ($i = 1; $i <= $request->count; $i++) {
                $trn_num = $request->lpn_num_field;
                $trn_line = 'ref_line_' . $i;
                $lpn_line = 'lpn_line_' . $i;
                $item = 'item_' . $i;
                $item = utf8_encode(base64_decode($request->$item));
                $item = htmlspecialchars_decode($item);
                $item_desc = 'item_desc_' . $i;
                $qty_req = 'qty_transact_' . $i;
                $uom = 'qty_transact_uom_' . $i;
                $qty_to_ship = 'qty_input_' . $i;
                $uom_to_ship = 'qty_input_uom_' . $i;
                $lot_num = 'lot_num_' . $i;
                $palletLoc = $getPalletLoc->loc_num;
                $each_lpn_line = explode(",", $request->$lpn_line);

                // check lpn loc freeze
                $check_to_loc = ItemLoc::where('whse_num', $request->whse_num)->where('loc_num', $palletLoc)->where('item_num', $item)->value('freeze');
                if ($check_to_loc == 'Y') {
                    throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $item, 'resource2' => $palletLoc])]);
                }

                //Get Item qty bal
                $itemQty = ItemWarehouse::select('qty_on_hand')->where('whse_num', $request->whse_num)->where('item_num', $item)->first();
                if ($request->$qty_to_ship > 0) {
                    if (count($each_lpn_line) > 1) {
                        foreach ($each_lpn_line as $each_line) {
                            $lpn_each_qty = 'lpn_each_qty' . $each_line;
                            $record[$i][$each_line] =
                                [
                                    'whse_num' => $request->to_whse,
                                    'from_whse' => $request->whse_num,
                                    'trn_num' => $request->trn_num,
                                    'trn_line' => $request->$trn_line,
                                    'lpn_num' => $lpnNum,
                                    'lpn_line' => $request->$lpn_line,
                                    'from_loc' => $getPalletLoc->loc_num,
                                    'loc_num' => $request->trn_loc,
                                    'lot_num' => $request->$lot_num,
                                    'item_num' => $item,
                                    'item_desc' => $request->$item_desc,
                                    'qty_req' => $request->$qty_req,
                                    'base_uom' => $request->$uom,
                                    'qty_input' => $request->$lpn_each_qty,
                                    'uom' => $request->$uom_to_ship,
                                    'each_lpn_line' => $each_line,
                                ];

                            $toShip[$i][$each_line] =
                                [
                                    'site_id' => auth()->user()->site_id,
                                    'cust_num' => $request->cust_num,
                                    'trn_num' => $request->trn_num,
                                    'trn_line' => $request->$trn_line,
                                    'item_num' => $item,
                                    'qty_shipped' => $request->$qty_to_ship,
                                    'from_whse' => $request->whse_num,
                                    'whse_num' => $request->to_whse,
                                    'trn_lot' => @$request->$lot_num ?? NULL,
                                    'shipment_id' => 0,
                                    'base_uom' => $request->$uom,
                                    'uom' => $request->$uom_to_ship,
                                    'qty_current' => $itemQty->qty_on_hand,
                                    'pick_uniqekey' => base64_encode($request->whse_num . $request->trn_num . $request->$trn_line) ?? NULL,
                                    'created_by' => auth()->user()->name,
                                    'modified_by' => auth()->user()->name,
                                    'from_loc' => $getPalletLoc->loc_num,
                                    'trn_loc' => $request->trn_loc,
                                    'lpn_num' => $lpnNum,
                                    'lpn_line' => $request->$lpn_line,
                                    'qty_input' => $request->$lpn_each_qty,
                                    'qty_input_uom' => $request->$uom_to_ship,
                                    'each_lpn_line' => $each_line,
                                ];
                        }
                    } else {
                        $record[$i] =
                            [
                                'whse_num' => $request->to_whse,
                                'from_whse' => $request->whse_num,
                                'trn_num' => $request->trn_num,
                                'trn_line' => $request->$trn_line,
                                'lpn_num' => $lpnNum,
                                'lpn_line' => $request->$lpn_line,
                                'from_loc' => $getPalletLoc->loc_num,
                                'loc_num' => $request->trn_loc,
                                'lot_num' => $request->$lot_num,
                                'item_num' => $item,
                                'item_desc' => $request->$item_desc,
                                'qty_req' => $request->$qty_req,
                                'base_uom' => $request->$uom,
                                'qty_input' => $request->$qty_to_ship,
                                'uom' => $request->$uom_to_ship,
                            ];

                        $toShip[$i] =
                            [
                                'site_id' => auth()->user()->site_id,
                                'cust_num' => $request->cust_num,
                                'trn_num' => $request->trn_num,
                                'trn_line' => $request->$trn_line,
                                'item_num' => $item,
                                'qty_shipped' => $request->$qty_to_ship,
                                'from_whse' => $request->whse_num,
                                'whse_num' => $request->to_whse,
                                'trn_lot' => @$request->$lot_num ?? NULL,
                                'shipment_id' => 0,
                                'base_uom' => $request->$uom,
                                'uom' => $request->$uom_to_ship,
                                'qty_current' => $itemQty->qty_on_hand,
                                'pick_uniqekey' => base64_encode($request->whse_num . $request->trn_num . $request->$trn_line) ?? NULL,
                                'created_by' => auth()->user()->name,
                                'modified_by' => auth()->user()->name,
                                'from_loc' => $getPalletLoc->loc_num,
                                'trn_loc' => $request->trn_loc,
                                'lpn_num' => $lpnNum,
                                'lpn_line' => $request->$lpn_line,
                                'qty_input' => $request->$qty_to_ship,
                                'qty_input_uom' => $request->$uom_to_ship,

                            ];
                    }
                }
            }

            // dd($record,$toShip);
            // $sendResponseFrom = TransferOrderShippingController::processPalletToTrans(config('icapt.transtype.to_shipping_from'),$record);
            //  exit;
            if (!empty($record)) {
                $sendResponseFrom = TransferOrderShippingController::processPalletToTrans(config('icapt.transtype.to_shipping_from'), $record);



                $sendResponseTo = TransferOrderShippingController::processPalletToTrans(config('icapt.transtype.to_shipping_to'), $record);
                $updateTO = TransferOrderShippingController::palletToProcess($toShip);
                $updatePalletLoc = PalletService::updatePalletLoc($lpnNum, $request->trn_loc, $request->to_whse, 'TO Ship');

                Alert::success('Success', __('success.processed', ['process' => __('Transfer Order Shipping')]))->persistent('Close');
                Session::put('modulename', 'ToShip');
                Session::put('trn_num', $request->trn_num);
                Session::put('whse_num', $request->whse_num);

                DB::commit();

                // SAP Intergration
                $tparm = new TparmView;
                $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

                if ($sap_trans_order_integration == 1 && ($getPalletLoc->loc_num != $request->trn_loc)) {
                    $lpnnum = $request->lpn_num_field;
                    $result = SapCallService::postPalletLPN($lpnnum, 'TO Ship');
                    if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                        if ($result != 200) {
                            Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                        }
                    }
                }

                $transferHeader = TransferOrder::select('to_whse', 'trn_loc')->where('trn_num', $request->trn_num)->where('from_whse', $request->whse_num)->first();

                DB::table('containers')->where('lpn_num', $lpnNum)->update([
                    'status' => 'Transferred',
                    'whse_num' => $transferHeader->to_whse,
                    'loc_num' => $transferHeader->trn_loc,
                ]);

                $list = new TransferLine();
                $trndetails = $list->with('item')->where('trn_num', $request->trn_num)->where('trn_line', $request->$trn_line)->where('from_whse', $request->whse_num)->where('site_id', auth()->user()->site_id)->first();

                if ($request->lot_num != null) {
                    // Generate barcode
                    $input = BarcodeController::GetTOShippingLabelData($request->whse_num, $request->to_whse, $request->trn_num, $request->$trn_line, $trndetails->item_num, null, $getPalletLoc->loc_num, $request->lot_num, $request->$qty_to_ship, $request->$uom_to_ship,  null, null, 'TranOrderShipping', null);
                } else {
                    // Generate barcode
                    $input = BarcodeController::GetTOShippingLabelData($request->whse_num, $request->to_whse, $request->trn_num, $request->$trn_line, $trndetails->item_num, null, $getPalletLoc->loc_num, $request->lot_num, $request->$qty_to_ship, $request->$uom_to_ship,  null, null, 'TranOrderShipping', null);
                }

                $tparm = new TparmView;
                $print_label = $tparm->getTparmValue('TOShipping', 'print_label');

                // dd($print_label);

                if ($print_label == 1) {
                    return BarcodeController::showLabelDefinition($input);
                } else {
                    $getTotalLineTo = TransferLine::where('trn_num', $request->trn_num)->whereRaw('(IFNULL(qty_required,0) - IFNULL(qty_shipped,0)) > 0')->count();
                    if (count($record) < $getTotalLineTo) {
                        return redirect()->route('backTOShippingList', ['whse_num' => $request->whse_num, 'trn_num' => $request->trn_num, 'item_num' => '']);
                    } else {
                        return redirect()->route('TransferOrderShipping');
                    }
                }
            } else {
                // return redirect()->back()->withInput()->withErrors(["Selected Pallet is empty. Please select other Pallet"]);
                throw ValidationException::withMessages(["Selected Pallet is empty. Please select other Pallet"]);
            }
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function processPalletToTrans($transtype, $record)
    {
        // $recod_key = array_keys($record);
        // $getLpnDetailsQTY = ContainerItem::where('lpn_num', $record[$recod_key[0]]['lpn_num'])->orderBy('lpn_line', 'ASC')->get();

        // $arrGetTrnLine = array();

        // $arrGetTrnLinebaseuom = array();
        // $arrGetTrnLineuom = array();


        // // Get the line by item_num
        // foreach($record as $key => $recordkey)
        // {
        //     $arrGetTrnLine[$recordkey['item_num'].$recordkey['uom']] = $recordkey['trn_line'];
        //     $arrGetTrnLinebaseuom[$recordkey['item_num'].$recordkey['uom']] = $recordkey['base_uom'];
        //     $arrGetTrnLineuom[$recordkey['item_num'].$recordkey['uom']] = $recordkey['uom'];
        // }

        // $record[$i] =
        //             [
        //                 'whse_num' => $request->to_whse,
        //                 'from_whse' => $request->whse_num,
        //                 'trn_num' => $request->trn_num,
        //                 'trn_line' => $request->$trn_line,
        //                 'lpn_num' => $lpnNum,
        //                 'lpn_line' => $request->$lpn_line,
        //                 'from_loc' => $getPalletLoc->loc_num,
        //                 'loc_num' => $request->trn_loc,
        //                 'lot_num' => $request->$lot_num,
        //                 'item_num' => $request->$item,
        //                 'item_desc' => $request->$item_desc,
        //                 'qty_req' => $request->$qty_req,
        //                 'base_uom' => $request->$uom,
        //                 'qty_input' => $request->$qty_to_ship,
        //                 'uom' => $request->$uom_to_ship,
        //             ];


        // "whse_num" => "Main"
        // "from_whse" => "MAINWH"
        // "trn_num" => "FA-160001"
        // "trn_line" => "1"
        // "lpn_num" => "FA-160001"
        // "lpn_line" => "1"
        // "from_loc" => "A1"
        // "loc_num" => "Transit"
        // "lot_num" => "1"
        // "item_num" => "FA-160001"
        // "item_desc" => "FA-160001"
        // "qty_req" => "200.0000"
        // "base_uom" => "EA"
        // "qty_input" => "200"
        // "uom" => "EA"


        // $arrStoreData = array();
        // $intIndex = 1;
        // foreach($getLpnDetailsQTY as $dataDatails)
        // {



        //     $arrStoreData[$intIndex]['whse_num'] = $record[$recod_key[0]]['whse_num'];
        //     $arrStoreData[$intIndex]['from_whse'] = $record[$recod_key[0]]['from_whse'];
        //     $arrStoreData[$intIndex]['trn_num'] = $record[$recod_key[0]]['trn_num'];
        //     $arrStoreData[$intIndex]['trn_line'] =  $arrGetTrnLine[$dataDatails->item_num.$dataDatails->uom];
        //     $arrStoreData[$intIndex]['lpn_num'] = $record[$recod_key[0]]['lpn_num'];
        //     $arrStoreData[$intIndex]['lpn_line'] = $dataDatails->lpn_line;
        //     $arrStoreData[$intIndex]['from_loc'] = $record[$recod_key[0]]['from_loc'];
        //     $arrStoreData[$intIndex]['loc_num'] = $record[$recod_key[0]]['loc_num'];

        //     if(@$dataDatails->lot_num)
        //     {
        //         $arrStoreData[$intIndex]['lot_num'] = @$dataDatails->lot_num;
        //     }
        //     else
        //     {
        //         $arrStoreData[$intIndex]['lot_num'] = "";
        //     }


        //     $arrStoreData[$intIndex]['item_num'] = $dataDatails->item_num;

        //     $arrStoreData[$intIndex]['item_desc'] = $dataDatails->item_desc;
        //     $arrStoreData[$intIndex]['qty_req'] = $dataDatails->qty_contained;
        //     $arrStoreData[$intIndex]['base_uom'] = $arrGetTrnLinebaseuom[$dataDatails->item_num.$dataDatails->uom];
        //     $arrStoreData[$intIndex]['qty_input'] =$dataDatails->qty_contained;
        //     $arrStoreData[$intIndex]['uom'] = $arrGetTrnLineuom[$dataDatails->item_num.$dataDatails->uom];


        //     $intIndex++;
        // }
        //dd("hahah-",$record,$arrStoreData);


        // New Function
        $recod_key = array_keys($record);
        $arrStoreData = array();
        $intIndex = 1;
        $intLpnIndex = 1;
        // dd($record);
        foreach ($record as $dataDatails) {
            //check if contains more than 1 lpn line
            if (count($dataDatails) > 1) {
                //foreach($dataDatails as $key => $lpnDet){
                $arrStoreData[$intLpnIndex]['whse_num'] = $dataDatails['whse_num'];
                $arrStoreData[$intLpnIndex]['from_whse'] = $dataDatails['from_whse'];
                $arrStoreData[$intLpnIndex]['trn_num'] = $dataDatails['trn_num'];
                $arrStoreData[$intLpnIndex]['trn_line'] = $dataDatails['trn_line'];
                $arrStoreData[$intLpnIndex]['lpn_num'] = $dataDatails['lpn_num'];
                $arrStoreData[$intLpnIndex]['lpn_line'] = $dataDatails['lpn_line'];
                $arrStoreData[$intLpnIndex]['from_loc'] = $dataDatails['from_loc'];
                $arrStoreData[$intLpnIndex]['loc_num'] = $dataDatails['loc_num'];
                $arrStoreData[$intLpnIndex]['lot_num'] = ContainerItem::where('lpn_num', $dataDatails['lpn_num'])->where('lpn_line', $dataDatails['lpn_line'])->value('lot_num') ?? '';
                $arrStoreData[$intLpnIndex]['item_num'] = $dataDatails['item_num'];

                $arrStoreData[$intLpnIndex]['item_desc'] = $dataDatails['item_desc'];
                $arrStoreData[$intLpnIndex]['qty_req'] = $dataDatails['qty_req'];
                $arrStoreData[$intLpnIndex]['base_uom'] = $dataDatails['base_uom'];
                $arrStoreData[$intLpnIndex]['qty_input'] = $dataDatails['qty_input'];
                $arrStoreData[$intLpnIndex]['uom'] = $dataDatails['uom'];

                $intLpnIndex++;
                //}
            } else {
                // Check LPN Line
                $getLPNCount = explode(",", $dataDatails['lpn_line']);
                $getLpnDetailsQTY = ContainerItem::where('lpn_num', $dataDatails['lpn_num'])->whereIn('lpn_line', $getLPNCount)->orderBy('lpn_line', 'ASC')->get();
                foreach ($getLpnDetailsQTY as $getLpnDetail) {
                    $arrStoreData[$intLpnIndex]['whse_num'] = $record[$intIndex]['whse_num'];
                    $arrStoreData[$intLpnIndex]['from_whse'] = $record[$intIndex]['from_whse'];
                    $arrStoreData[$intLpnIndex]['trn_num'] = $record[$intIndex]['trn_num'];
                    $arrStoreData[$intLpnIndex]['trn_line'] = $record[$intIndex]['trn_line'];
                    $arrStoreData[$intLpnIndex]['lpn_num'] = $getLpnDetail->lpn_num;
                    $arrStoreData[$intLpnIndex]['lpn_line'] = $getLpnDetail->lpn_line;
                    $arrStoreData[$intLpnIndex]['from_loc'] = $record[$intIndex]['from_loc'];
                    $arrStoreData[$intLpnIndex]['loc_num'] = $record[$intIndex]['loc_num'];
                    $arrStoreData[$intLpnIndex]['lot_num'] = $getLpnDetail->lot_num;
                    $arrStoreData[$intLpnIndex]['item_num'] = $record[$intIndex]['item_num'];

                    $arrStoreData[$intLpnIndex]['item_desc'] = $record[$intIndex]['item_desc'];
                    $arrStoreData[$intLpnIndex]['qty_req'] = $record[$intIndex]['qty_req'];
                    $arrStoreData[$intLpnIndex]['base_uom'] = $record[$intIndex]['base_uom'];
                    $arrStoreData[$intLpnIndex]['qty_input'] = $record[$intIndex]['qty_input'];
                    $arrStoreData[$intLpnIndex]['uom'] = $getLpnDetail->uom;

                    $intLpnIndex++;
                }
            }
            $intIndex++;
        }

        foreach ($arrStoreData as $datas) {
            if ($datas['qty_input'] > 0) {
                $transData = new Request($datas);
                $sendResponse = PalletService::palletMatlTrans($transtype, $transData);
            }
        }
        return 'true';
    }

    public function palletToProcess($record)
    {
        //dd("Check",$record);
        $recod_key = array_keys($record);
        //     $getLpnDetailsQTY = ContainerItem::where('lpn_num', $record[$recod_key[0]]['lpn_num'])->orderBy('lpn_line', 'ASC')->get();
        //     $arrGetTrnLine = array();


        //     // Get the line by item_num
        //     foreach($record as $key => $recordkey)
        //     {
        //         $arrGetTrnLine[$recordkey['item_num'].$recordkey['uom']] = $recordkey['trn_line'];
        //     }
        //    //dd($record,$getLpnDetailsQTY);
        //     $index = 0;
        //     foreach($getLpnDetailsQTY as $dataline){
        //         $recordMashup[$index]['site_id'] = $dataline->site_id;
        //         $recordMashup[$index]['cust_num'] =  $record[$recod_key[0]]['cust_num'];
        //         $recordMashup[$index]['trn_num'] = $record[$recod_key[0]]['trn_num'];
        //         $recordMashup[$index]['trn_line'] = $arrGetTrnLine[$dataline->item_num.$dataline->uom];
        //         $recordMashup[$index]['item_num'] = $dataline->item_num;
        //         $recordMashup[$index]['qty_shipped'] = $dataline->qty_contained;
        //         $recordMashup[$index]['from_whse'] = $record[$recod_key[0]]['from_whse'];
        //         $recordMashup[$index]['whse_num'] = $record[$recod_key[0]]['whse_num'];

        //         if(@$dataline->lot_num)
        //         {
        //             $recordMashup[$index]['trn_lot'] = $dataline->lot_num;
        //         }
        //         else
        //         {
        //             $recordMashup[$index]['trn_lot'] = null;
        //         }


        //         $recordMashup[$index]['shipment_id'] = 0;
        //         $recordMashup[$index]['base_uom'] = $record[$recod_key[0]]['base_uom'];;
        //         $recordMashup[$index]['uom'] = $dataline->uom;
        //         $recordMashup[$index]['qty_current'] = $record[$recod_key[0]]['qty_current'];
        //         $recordMashup[$index]['pick_uniqekey'] = $record[$recod_key[0]]['pick_uniqekey'];
        //         $recordMashup[$index]['created_by'] = $record[$recod_key[0]]['created_by'];
        //         $recordMashup[$index]['modified_by'] = $record[$recod_key[0]]['modified_by'];
        //         $recordMashup[$index]['from_loc'] = $record[$recod_key[0]]['from_loc'];
        //         $recordMashup[$index]['trn_loc'] = $record[$recod_key[0]]['trn_loc'];
        //         $recordMashup[$index]['lpn_num'] = $record[$recod_key[0]]['lpn_num'];
        //         $index++;
        //     }

        $arrStoreData = array();
        $intIndex = 1;
        $intLpnIndex = 1;
        foreach ($record as $dataline) {
            //check if contains more than 1 lpn line
            if (count($dataline) > 1) {
                // foreach($dataline as $key => $lpnlineDet){
                $recordMashup[$intLpnIndex]['site_id'] = $dataline['site_id'];
                $recordMashup[$intLpnIndex]['cust_num'] = $dataline['cust_num'];
                $recordMashup[$intLpnIndex]['trn_num'] = $dataline['trn_num'];
                $recordMashup[$intLpnIndex]['trn_line'] = $dataline['trn_line'];
                $recordMashup[$intLpnIndex]['item_num'] = $dataline['item_num'];
                $recordMashup[$intLpnIndex]['qty_shipped'] = $dataline['qty_input'];
                $recordMashup[$intLpnIndex]['from_whse'] = $dataline['from_whse'];
                $recordMashup[$intLpnIndex]['whse_num'] = $dataline['whse_num'];
                $recordMashup[$intLpnIndex]['trn_lot'] = ContainerItem::where('lpn_num', $dataline['lpn_num'])->where('lpn_line', $dataline['lpn_line'])->value('lot_num') ?? '';
                $recordMashup[$intLpnIndex]['shipment_id'] = 0;
                $recordMashup[$intLpnIndex]['lpn_line'] = $dataline['lpn_line'];
                $recordMashup[$intLpnIndex]['base_uom'] = $dataline['base_uom'];
                $recordMashup[$intLpnIndex]['uom'] = ContainerItem::where('lpn_num', $dataline['lpn_num'])->where('lpn_line', $dataline['lpn_line'])->value('uom') ?? '';
                $recordMashup[$intLpnIndex]['qty_current'] = $dataline['qty_current'];
                $recordMashup[$intLpnIndex]['pick_uniqekey'] = $dataline['pick_uniqekey'];
                $recordMashup[$intLpnIndex]['created_by'] = $dataline['created_by'];
                $recordMashup[$intLpnIndex]['modified_by'] = $dataline['modified_by'];
                $recordMashup[$intLpnIndex]['from_loc'] = $dataline['from_loc'];
                $recordMashup[$intLpnIndex]['trn_loc'] = $dataline['trn_loc'];
                $recordMashup[$intLpnIndex]['lpn_num'] = $dataline['lpn_num'];

                $intLpnIndex++;
                //}
            } else {
                // Check LPN Line
                $getLPNCount = explode(",", $dataline['lpn_line']);
                $getLpnDetailsQTY = ContainerItem::where('lpn_num', $dataline['lpn_num'])->whereIn('lpn_line', $getLPNCount)->orderBy('lpn_line', 'ASC')->get();
                foreach ($getLpnDetailsQTY as $getLpnDetail) {
                    $recordMashup[$intLpnIndex]['site_id'] = $record[$intIndex]['site_id'];
                    $recordMashup[$intLpnIndex]['cust_num'] = $record[$intIndex]['cust_num'];
                    $recordMashup[$intLpnIndex]['trn_num'] = $record[$intIndex]['trn_num'];
                    $recordMashup[$intLpnIndex]['trn_line'] = $record[$intIndex]['trn_line'];
                    $recordMashup[$intLpnIndex]['item_num'] = $record[$intIndex]['item_num'];
                    $recordMashup[$intLpnIndex]['qty_shipped'] = $record[$intIndex]['qty_input'];
                    $recordMashup[$intLpnIndex]['from_whse'] = $record[$intIndex]['from_whse'];
                    $recordMashup[$intLpnIndex]['whse_num'] = $record[$intIndex]['whse_num'];
                    $recordMashup[$intLpnIndex]['trn_lot'] = $getLpnDetail->lot_num;
                    $recordMashup[$intLpnIndex]['shipment_id'] = 0;
                    $recordMashup[$intLpnIndex]['lpn_line'] = $getLpnDetail->lpn_line;
                    $recordMashup[$intLpnIndex]['base_uom'] = $record[$intIndex]['base_uom'];
                    $recordMashup[$intLpnIndex]['uom'] = $getLpnDetail->uom;
                    $recordMashup[$intLpnIndex]['qty_current'] = $record[$intIndex]['qty_current'];
                    $recordMashup[$intLpnIndex]['pick_uniqekey'] = $record[$intIndex]['pick_uniqekey'];
                    $recordMashup[$intLpnIndex]['created_by'] = $record[$intIndex]['created_by'];
                    $recordMashup[$intLpnIndex]['modified_by'] = $record[$intIndex]['modified_by'];
                    $recordMashup[$intLpnIndex]['from_loc'] = $record[$intIndex]['from_loc'];
                    $recordMashup[$intLpnIndex]['trn_loc'] = $record[$intIndex]['trn_loc'];
                    $recordMashup[$intLpnIndex]['lpn_num'] = $getLpnDetail->lpn_num;

                    $intLpnIndex++;
                }
            }
            $intIndex++;
        }

        //  dd("11here",$recordMashup);

        //exit;


        $checkTrnNum = TransferOrder::where('trn_num', $record[$recod_key[0]]['trn_num'] ?? $record[$recod_key[0]][1]['trn_num'])->where('status', '!=', "C")->first();
        $transDate = Carbon::now()->toDateTimeString();
        Session::put('timestamp', $transDate);
        Session::put('transferOrderShipping', $checkTrnNum);

        // $rank = app('App\Http\Controllers\ApiController')->getnextRank(base64_encode($recordMashup[1]['whse_num']), base64_encode($recordMashup[0]['item_num']), base64_encode($recordMashup[0]['trn_loc']));

        //dd($rank,$recordMashup[0]['whse_num'],$recordMashup[0]['item_num'],$recordMashup[0]['trn_loc']);
        $toArr = [];
        $fromArr = [];
        // $index = 0;
        $i = 0;

        // dd($recordMashup);

        foreach ($recordMashup as $datas) {
            if ($datas['qty_shipped'] > 0) {

                $transData = new Request($datas);
                $transData['ref_num'] = $transData->trn_num;
                $transData['ref_line'] = $transData->trn_line;
                $transData['qty'] = $transData->qty_shipped;
                $tran_loc = $transData->trn_loc;
                $tran_lot = @$transData->trn_lot ?? null;
                $requestTo = clone $transData;
                // dd($recordMashup);
                // New Data
                $baseuom = Item::where('item_num', $datas['item_num'])->value('uom');
                $selectuom = $datas['base_uom'];
                $lineuom = $datas['uom'];
                $qty = $datas['qty_shipped'];

                $whse_num = $datas['from_whse'];
                $to_whse = $datas['whse_num'];
                $loc_num = $datas['from_loc'];
                $to_loc = $datas['trn_loc'];
                $item_num = $datas['item_num'];
                $trn_line = $datas['trn_line'];
                $trn_num = $datas['trn_num'];
                $lpn_num = $datas['lpn_num'];
                $rank = app('App\Http\Controllers\ApiController')->getnextRank(base64_encode($datas['whse_num']), base64_encode($datas['item_num']), base64_encode($datas['trn_loc']));
                $convertUom = UomConv::convertUOM($baseuom, $selectuom, $lineuom, $qty, $item_num, '', '', '');

                // dd($convertUom,$baseuom, $selectuom, $lineuom, $qty, $item_num, $datas);
                $qtyConvert = $convertUom['conv_qty_to_base']['qty'];
                $uomConvert = $convertUom['conv_qty_to_base']['uom'];

                //$updateFromItemLocation = GeneralService::updateItemLocationQty($whse_num, $loc_num, $item_num, -$qtyConvert, $tran_lot, $uomConvert, 0, null);
                $fromArr[$i] = [
                    'whse_num' => $whse_num,
                    'loc_num' => $loc_num,
                    'item_num' =>  $item_num,
                    'qty_conv' => $qtyConvert,
                    'lot_num'  => @$tran_lot ?? null

                ];

                $updateFromItemLocation = PalletService::updateItemLocLotQty($fromArr[$i], $toArr = [], 0, 'From TO Shipping');


                $toArr[$i] = [
                    'whse_num' => $to_whse,
                    'loc_num'  => $to_loc,
                    'item_num' =>  $item_num,
                    'qty_conv' => $qtyConvert,
                    'lot_num'  => @$tran_lot ?? null,
                    'uom_conv' =>  $uomConvert,
                    'rank' =>  $rank

                ];
                $updateToItemLocation = PalletService::updateItemLocLotQty($fromArr = [], $toArr[$i], 0, 'To TO Shipping');
                $updateTransOrder = GeneralService::updatePalletTransOrderShipping($trn_num, $trn_line, $qty, $tran_lot);
                $addUpdateTransferOrder = GeneralService::addUpdateTransferOderLot($qty, $tran_loc, $item_num, $selectuom, $tran_lot, $trn_num, $trn_line, $lpn_num);
            }

            $i++;
        }

        if ($i == $intLpnIndex) {
            return 'true';
        }
    }

    /**
     * Validates transfer order shipping data
     * @param array|Request $request Request data array or Request object
     * @return array [errors, validatedRequest]
     */
    public static function toShipValidation($request)
    {
        $tparm = new TparmView;
        $errors = [];

        // Convert Request object to array if needed
        if ($request instanceof Request) {
            $requestData = $request->all();
            $validatedRequest = $request;
        } else {
            $requestData = $request;
            $validatedRequest = $request;
        }

        // Validate warehouse
        if (!empty($requestData['whse_num'])) {
            $result = ValidationController::checkWhseValidation($requestData['whse_num']);
            if ($result !== true) {
                $errors['whse_num'] = $result;
            }
        }

        // Validate transfer order line
        if (!empty($requestData['trn_num'])) {
            // Create a request object for validation methods that expect object properties
            $requestObject = (object) $requestData;
            $result = ValidationController::checkTOLineValidation($requestObject);
            if ($result !== true) {
                $errors['trn_num'] = $result;
            } else {
                $transLine = TransferLine::where('trn_num', $requestData['trn_num'])
                    ->where('trn_line', $requestData['trn_line'])
                    ->first();

                if ($transLine) {
                    // Update request with transfer line data
                    $validatedRequest['trn_num'] = $transLine->trn_num;
                    $validatedRequest['trn_line'] = $transLine->trn_line;
                    $validatedRequest['item_num'] = $transLine->item_num;
                    $validatedRequest['uom'] = $transLine->uom;
                    $validatedRequest['qty_to_ship'] = $transLine->qty_to_ship;
                    $validatedRequest['qty'] = $transLine->qty_to_ship;

                    // Validate item number
                    if (!empty($validatedRequest['item_num'])) {
                        $result = ValidationController::checkItemNumValidation(
                            $validatedRequest['item_num'],
                            $validatedRequest['whse_num']
                        );
                        if ($result !== true) {
                            $errors['item_num'] = $result;
                        }
                    }
                }
            }
        }

        // Validate location
        if (!empty($requestData['loc_num'])) {
            $result = ValidationController::checkTransitPickingLocValidtion($requestData, 'loc_num', true);
            if ($result !== true) {
                $errors['loc_num'] = $result;
            }
        }

        // Validate lot number if lot tracking is enabled
        if (!empty($requestData['lot_num']) && !empty($requestData['lot_tracked']) && $requestData['lot_tracked'] == 1) {
            $result = ValidationController::checkLotNumValidtion($requestData);
            if ($result !== true) {
                $errors['lot_num'] = $result;
            }
        }

        // Validate quantity
        if (!empty($requestData['qty_to_ship'])) {
            if ($requestData['qty_to_ship'] != ($requestData['qty'] ?? 0)) {
                $validatedRequest['qty'] = $requestData['qty_to_ship'];
            }

            // Create a copy of request data with qty field for validation
            $qtyValidationData = $requestData;
            $qtyValidationData['qty'] = $requestData['qty_to_ship'];

            $result = ValidationController::checkLotLocQtyValidtion($qtyValidationData);
            if ($result !== true) {
                $errors['qty_to_ship'] = $result;
            }
        }

        // Validate UOM conversion
        if (!empty($requestData['uom'])) {
            $validatedRequest['selected_uom'] = $requestData['uom'];
            $result = ValidationController::validateConvUOM($requestData);
            if ($result['result'] !== true) {
                $errors['uom'] = $result['msg'];
            }
        }

        return [$errors, $validatedRequest];
    }
}
