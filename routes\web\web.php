<?php

use Illuminate\Support\Facades\Mail;
use Spatie\Health\Checks\Result;

/*
  |--------------------------------------------------------------------------
  | Web Routes
  |--------------------------------------------------------------------------
  |
  | Here is where you can register web routes for your application. These
  | routes are loaded by the RouteServiceProvider within a group which
  | contains the "web" middleware group. Now create something great!
  |
 */



Route::resource('customer', 'CustomerController');
Route::resource('staff', 'StaffController');
Route::resource('student', 'StudentController');

Route::resource('migrator', 'MigratorController');

Route::post('migrator/deleteMig', 'MigratorController@deleteMig')->name('migrator.deleteMig');
Route::post('migrator/rollBackMig', 'MigratorController@rollBackMig')->name('migrator.rollBackMig');
Route::post('migrator/runMigration', 'MigratorController@runMigration')->name('migrator.runMigration');




Route::any('/bartendercloud/webhook', 'BartenderCloudController@getToken')->name('bartendercloud.webhook');
Route::any('/bartendercloud/disconnected', 'BartenderCloudController@disconnected')->name('bartendercloud.disconnected');
Route::get('/bartendercloud/connect', 'BartenderCloudController@connect')->name('bartendercloud.connect');
Route::get('/bartendercloud/test', 'BartenderCloudController@test')->name('bartendercloud.test');

Route::any('/quickbook/webhook', 'QuickbookController@webhookNotification')->name('quickbook.webhook');
Route::any('/quickbooks/webhook', 'QuickbookController@webhookNotification')->name('quickbooks.webhook');

Route::get('/console/processProductCode', 'ConsoleController@doProductCodesScanImport')->name('processProductCode');
Route::get('/console/processItem', 'ConsoleController@doItemScanImport')->name('processItem');
Route::get('/console/processLocation', 'ConsoleController@doLocationsScanImport')->name('processLocation');
Route::get('/console/processVendor', 'ConsoleController@doVendorsScanImport')->name('processVendor');
Route::get('/console/processPurchaseOrder', 'ConsoleController@doPurchaseOrdersScanImport')->name('processPurchaseOrder');
Route::get('/console/processCustomer', 'ConsoleController@doCustomerScanImport')->name('processCustomer');
Route::get('/console/processCustomerOrder', 'ConsoleController@doCustomerOrderScanImport')->name('processCustomerOrder');

Route::get('/license/getLicenseDetails/cust_name={cust_name}', 'LicenseController@doGetLicenseDetails')->name('doGetLicenseDetails');

Route::get('/webhook/testAPI', 'WebHookController@testAPI')->name('testAPI');
Route::get('/webhook/readjson', 'WebHookController@readjson')->name('readjson');

Route::post('/webhook/email', 'WebHookController@email')->name('email');
Route::get('/webhook/register', 'WebHookController@create')->name('register');
Route::get('/webhook/update', 'WebHookController@updateSubscription')->name('updateSubscription');
Route::get('/webhook/updateCreditCard', 'WebHookController@updateCreditCard')->name('updateCreditCard');
Route::post('/webhook/store', 'WebHookController@store')->name('storeDataInfo');
Route::post('/webhook/validation/existSite', 'WebHookController@existSite')->name('WebhookValidationSite');
Route::post('/webhook/validation/existUsername', 'WebHookController@existUsername')->name('WebhookValidationUsername');
Route::post('/webhook/validation/existEmail', 'WebHookController@existEmail')->name('WebhookValidationEmail');
Route::post('/webhook/validation/validPassword', 'WebHookController@validPassword')->name('WebhookValidationPassword');
Route::post('/webhook/validation/validSiteID', 'WebHookController@validSiteID')->name('WebhookValidationSiteID');
Route::post('/webhook/validation/validPhoneNum', 'WebHookController@validPhoneNum')->name('WebhookValidationPhoneNum');


Route::get('/webhook/subscription/plan_code={plan_code}', 'WebHookController@subscription')->name('subscription');
Route::get('/webhook/activeNewSite/{siteID}', 'WebHookController@activeNewSite')->name('activeNewSite');

Route::get('/webhook/newsubscription/plan_code={plan_code}', 'WebHookController@newsubscription')->name('newsubscription');
Route::get('/signup/plan_code={plan_code}', 'WebHookController@newsubscription')->name('signup');
Route::get('/webhook/thankspage', 'WebHookController@thankspage')->name('thankspage');
Route::get('/webhook/verifyemailacc/token_code={token_code}', 'WebHookController@verifyemailacc')->name('verifyemailacc');
Route::get('/webhook/finalstepssubscription', 'WebHookController@finalstepssubscription')->name('finalstepssubscription');

Route::post('/my/signup/{plan_name?}', 'WebHookController@newsubpurl')->name('newsubpurl');

Route::post('/webhook/newstorebussinesinfo', 'WebHookController@newstorebussinesinfo')->name('newstorebussinesinfo');
Route::post('/webhook/createnewsite', 'WebHookController@createnewsite')->name('createnewsite');
Route::post('/webhook/newstore', 'WebHookController@newstore')->name('newstore');
Route::get('/webhook/retrieveHostPage/site_id={site_id}', 'WebHookController@retrieveHostPage')->name('retrieveHostPage');

Route::get('/webhook/creditcardpage', 'WebHookController@creditcardpage')->name('creditcardpage');


Route::get('/webhook/autoLoginSucess', 'WebHookController@autoLoginSucess')->name('autoLoginSucess');

Route::get('/webhook/checkURL', 'WebHookController@checkURL')->name('webhook.checkURL'); // Check URL

Route::get('/email-test/{mail}', function ($mail) {

    $details['email'] = $mail;
    for ($i = 0; $i < 2; $i++) {
        dispatch(new App\Jobs\SendEmailJob($details));
    }

    //  dd('done');
    //   return  Mail::send('mail.testmail', [
    //         'name' => 'Reeve',
    //         'email' => '<EMAIL>'],
    //         function ($message) {
    //                 $message->from('<EMAIL>');
    //                 $message->to('<EMAIL>', 'Reeve Perk')
    //                         ->subject('ICAPT Test Mail fffff');
    //     });
});

Route::get('lang/{lang}', ['as' => 'lang.switch', 'uses' => 'App\Http\Controllers\LanguageController@switchLang']);

Route::post('getCallbackResults/{subid}', function ($subid) {
    Storage::put('resultres.txt', $subid);
    //$details['email'] = '<EMAIL>';
    //$data = file_get_contents("http://reeve.icapt.site/getCallbackResults");
    //$datafiltered = json_decode($data);
    //dispatch(new App\Jobs\SendEmailJob($details));
});

Route::post('reset/verify_code/{unique_id?}', 'Auth\ResetLockController@verifyCode')->name('reset.verify_code');
Route::post('reset/verify_login/{code?}', 'Auth\ResetLockController@verifyLogin')->name('reset.verify_login');
Route::post('reset/expiry_code/{unique_id?}', 'Auth\ResetLockController@expiryCode')->name('reset.expiry_code');
Route::get('reset/autologin', 'Auth\ResetLockController@autologin')->name('autologin');


Auth::routes();
Route::get('/quickbooks/disconnected', 'QuickbookController@disconnected')->name('quickbook.disconnected');

Route::group(['middleware' => ['auth']], function () {
    Route::get('/logout', 'Auth\LoginController@logout')->name('logout');
    Route::get('/stop-session', 'MenuController@stopPreviousSession')->name('stopPreviousSession');
    Route::get('password/expired', 'Auth\ExpiredPasswordController@expired')->name('password.expired');
    Route::post('password/post_expired', 'Auth\ExpiredPasswordController@postExpired')->name('password.post_expired');
});

Route::group(['middleware' => ['auth']], function () {
    // --- UI Demo Routes ---
    Route::get('/demo/ui-smartscan', function () {
        return view('demo.ui_smartscan');
    })->name('demo.ui_smartscan');
    Route::get('/demo/ui-unified', function () {
        return view('demo.ui_unified');
    })->name('demo.ui_unified');
    Route::get('/demo/ui-stepper', function () {
        return view('demo.ui_stepper');
    })->name('demo.ui_stepper');
    Route::get('/demo/ui-tabbed', function () {
        return view('demo.ui_tabbed');
    })->name('demo.ui_tabbed');

    // 404 fallback
    Route::fallback(function () {
        return view('errors.404');
    });
    Route::any('/bartendercloud/token', 'BartenderCloudController@getToken')->name('bartendercloud.token');
    Route::get('/bartendercloud/disconnect', 'BartenderCloudController@disconnect')->name('bartendercloud.disconnect');

    // Logout
    Route::get('/logout', 'Auth\LoginController@logout')->name('logout');

    Route::get('/createdoc-new/{ref_type?}/{ref_num?}/{suffix?}/{ref_line?}/{ref_rel?}', 'DocNoteController@createdocnew')->name('doc.createnew');
    Route::get('/createdoc/{ref_type?}/{ref_num?}/{ref_line?}/{ref_rel?}', 'DocNoteController@createdoc')->name('doc.create');
    Route::get('/createnote-new/{ref_type?}/{ref_num?}/{suffix?}/{ref_line?}/{ref_rel?}', 'DocNoteController@createnotenew')->name('note.createnew');
    Route::get('/createnote/{ref_type?}/{ref_num?}/{ref_line?}/{ref_rel?}', 'DocNoteController@createnote')->name('note.create');
    Route::get('/view-new/{ref_type?}/{ref_num?}/{suffix?}/{ref_line?}/{ref_rel?}', 'DocNoteController@viewnotenew')->name('docnote.viewnew');
    Route::get('/view/{ref_type?}/{ref_num?}/{ref_line?}/{ref_rel?}', 'DocNoteController@viewnote')->name('docnote.view');


    Route::get('/media', 'DocNoteController@showMedia')->name('showMedia');

    Route::post('/create', 'DocNoteController@store')->name('docnote.store');
    Route::post('/note/create', 'DocNoteController@storeNote')->name('docnote.storeNote');
    Route::put('/docnote/{docNote}', 'DocNoteController@update')->name('docnote.update');

    Route::delete('/docnote/{docNote}', 'DocNoteController@destroy')->name('docnote.delete');

    Route::get('/quickbooks/test', 'QuickbookController@test')->name('quickbook.test');

    Route::get('/quickbooks/disconnect', 'QuickbookController@disconnect')->name('quickbook.disconnect');

    Route::get('/quickbooks/connect', 'QuickbookController@connect')->name('quickbook.connect');
    Route::get('/quickbooks/token', 'QuickbookController@getToken')->name('quickbook.token');

    Route::get('/salesmarketing', 'SalesMarketing\SalesMarketingController@index')->name('salesmarketing');
    Route::post('/salesmarketing/generate', 'SalesMarketing\SalesMarketingController@generateURL')->name('salesmarketing.generate');
    Route::get('/salesmarketing/view', 'SalesMarketing\SalesMarketingController@view')->name('salesmarketing.view');

    Route::post('/createSASupport', 'Super\SuperController@createSASupport')->name('super.createSASupport'); // Super user
    Route::post('/resetSASupportPassword', 'Super\SuperController@resetSASupportPassword')->name('super.resetSASupportPassword'); // Super user

    Route::get('/super', 'Super\SuperController@index')->name('super.index'); // Super user
    Route::post('/super', 'Super\SuperController@execute')->name('super.execute'); // Super user
    Route::post('/updatedata', 'Super\SuperController@update')->name('super.update'); // Super user
    Route::delete('/super', 'Super\SuperController@delete')->name('super.delete'); // Super user
    Route::get('/resetdata', 'Super\SuperController@reset')->name('super.reset');
    Route::get('/update/po', 'Super\UpdateController@update_po')->name('super.update_po');
    Route::get('/update/co', 'Super\UpdateController@update_co')->name('super.update_co');
    Route::post('/super', 'Super\SuperController@exeNewSite')->name('super.exeNewSite'); // Super user




    Route::get('/super/doresetdata', 'Super\SuperController@doResetDemoData')->name('super.doResetDemoData'); // Super user

    Route::get('/super/labels', 'Super\LabelController@index')->name('super.labels.index'); // Super user
    Route::get('/super/labels/{id}/edit', 'Super\LabelController@edit')->name('super.labels.edit'); // Super user
    Route::post('/super/labels/{id}/edit', 'Super\LabelController@update')->name('super.labels.update'); // Super user

    Route::get('/back_to_logged_user', 'Super\SuperController@login_back')->name('super.login_back'); // Super user

    Route::post('/login_as', 'Super\SuperController@login_as')->name('super.login_as'); // Super user
    Route::get('/super/labels/{id}/preview', 'Super\LabelController@preview')->name('super.labels.preview'); // Super user


    Route::post('/migrate_object', 'Super\SuperController@migrate_object')->name('super.migrate_object'); // Super user
    Route::post('/migrate_plan', 'Super\SuperController@migrate_plan')->name('super.migrate_plan'); // Super user

    Route::post('/convert_max_user', 'Super\SuperController@convert_max_user')->name('super.convert_max_user'); // Super user

    Route::post('/convert_plan_id', 'Super\SuperController@convert_plan_id')->name('super.convert_plan_id'); // Super user

    Route::post('/migrate_labels', 'Super\SuperController@do_migrate_label')->name('super.migrate_labels'); // Super user

    Route::post('/migrate_labels_without_product', 'Super\SuperController@do_migrate_label_without_production')->name('super.migrate_labels_without_product'); // Super user
    Route::post('/migrate_labels_modules', 'Super\SuperController@migrate_labels_modules')->name('super.migrate_labels_modules'); // Super user



    /*     * *****************
      Home Menu
     * ****************** */

    Route::get('/dashboard', 'MenuController@generalDashboard')->name('dashboard');
    Route::post('/dashboard', 'MenuController@updateGeneralDashboard')->name('dashboard.update');
    Route::get('/invDashboard', 'MenuController@invDashboard')->name('invDashboard');
    Route::get('/prodDashboard', 'MenuController@prodDashboard')->name('prodDashboard');

    Route::get('/', 'MenuController@webmenu')->name('web');
    Route::get('/web', 'MenuController@webMenuFromMobile')->name('webMenuFromMobile');
    Route::get('/home', 'MenuController@mobilemenu')->name('mobile');
    Route::get('/home/<USER>', 'MenuController@inquiry')->name('inquiry');
    Route::get('/home/<USER>', 'MenuController@incoming')->name('incoming');
    Route::get('/home/<USER>', 'MenuController@outgoing')->name('outgoing');
    Route::get('/home/<USER>', 'MenuController@warehouse')->name('whse');
    Route::get('/home/<USER>', 'MenuController@production_run')->name('production_run');
    Route::get('/home/<USER>', 'MenuController@production_inv')->name('production_inv');
    Route::get('/home/<USER>', 'MenuController@printlabel')->name('printlabel');
    Route::get('/home/<USER>', 'MenuController@pallet')->name('pallet');

    Route::get('/maintenance/master-files', 'MasterMaintenance\MenuController@masterMenu')->name('mastermenu');
    Route::get('/maintenance/transactions', 'MasterMaintenance\MenuController@transmenu')->name('transmenu');
    Route::get('/maintenance/utilities', 'MasterMaintenance\MenuController@utilitiesMenu')->name('utilitiesmenu');

    Route::get('/menu/history', 'MasterMaintenance\MenuController@historymenu')->name('historymenu');
    Route::get('/reports/inventory', 'MasterMaintenance\MenuController@inventoryMenu')->name('inventoryMenu');
    Route::get('/reports/production', 'MasterMaintenance\MenuController@productionMenu')->name('productionMenu');
    Route::get('/reports/transaction', 'MasterMaintenance\MenuController@transactionMenu')->name('transactionMenu');
    Route::get('/utilities/job-status-updater', 'MasterMaintenance\JobController@jobStatusUpdater')->name('jobStatusUpdater');
    Route::get('/utilities/job-status-updater/data', 'MasterMaintenance\JobController@jobStatusUpdaterData')->name('jobStatusUpdaterData');
    Route::post('/utilities/job-status-updater', 'MasterMaintenance\JobController@processJobStatusUpdater')->name('processJobStatusUpdater');

    Route::get('/utilities/inventory-count-unmatched', 'MasterMaintenance\InventoryCountUnMatchedController@index')->name('inventoryCountUnmatched');
    Route::get('/utilities/inventory-count-unmatched/{id}/edit', 'MasterMaintenance\InventoryCountUnMatchedController@edit')->name('inventoryCountUnmatched.edit');
    Route::post('/utilities/inventory-count-unmatched/{id}/edit', 'MasterMaintenance\InventoryCountUnMatchedController@update')->name('inventoryCountUnmatched.update');

    Route::get('/utilities/inventory-count-unmatched/validation', 'MasterMaintenance\InventoryCountUnMatchedController@validation')->name('checkInvCountValidation');
    Route::post('/utilities/inventory-count-unmatched/update', 'MasterMaintenance\InventoryCountUnMatchedController@update')->name('inventoryCountUnmatched.update');

    Route::post('/utilities/inventory-count-unmatched/process', 'MasterMaintenance\InventoryCountUnMatchedController@process')->name('inventoryCountUnmatched.process');
    // Route::post('/utilities/inventory-count-unmatched/export', 'MasterMaintenance\InventoryCountUnMatchedController@index')->name('inventoryCountUnmatched.process');

    // CO Utilities
    Route::get('/utilities/lot-expiry-date-updater', 'MasterMaintenance\LotExpiryDateController@lotExpiryDateUpdater')->name('LotExpiryDateUpdate');
    Route::get('/utilities/lot-expiry-date-updater/data', 'MasterMaintenance\LotExpiryDateController@lotExpiryDateUpdaterData')->name('LotExpiryDateUpdate.data');
    Route::post('/utilities/lot-expiry-date-updater', 'MasterMaintenance\LotExpiryDateController@lotExpiryDateUpdaterProcess')->name('LotExpiryDateUpdate.process');

    // TEST ITEMLIST V2
    Route::get('/live_search/action', 'LiveSearchController@action')->name('live_search.action');

    // Api Route
    Route::get('/getWhse/{whse_num?}', 'ApiController@getWhse')->name('getWhse');
    Route::get('/getPalletTransaction', 'ApiController@getPalletTransaction')->name('getPalletTransaction');
    Route::get('/getWhseIntSetting/{whse_num?}', 'ApiController@getWhseIntSetting')->name('getWhseIntSetting');
    Route::get('/getPackageType/{pkgtype_num?}', 'ApiController@getPackageType')->name('getPackageType');
    Route::get('/getEmployee', 'ApiController@getEmployee')->name('getEmployee');
    Route::get('/getVendLot', 'ApiController@getVendLot')->name('getVendLot');
    Route::get('/getDefaultOperNum', 'ApiController@getDefaultOperNum')->name('getDefaultOperNum');
    Route::get('/getDefaultOperNumNew', 'ApiController@getDefaultOperNumNew')->name('getDefaultOperNumNew');
    Route::get('/getIssuedLot/{from_module}/{whse_num}/{ref_num}/{ref_line}/{ref_release?}/', 'ApiController@getIssuedLot')->name('getIssuedLot');
    Route::get('/checkIssuedLot', 'ApiController@checkIssuedLot')->name('checkIssuedLot');
    Route::get('/getIssuedLotQty', 'ApiController@getIssuedLotQty')->name('getIssuedLotQty');
    Route::get('/convertAlternateBarcodetoItem', 'ApiController@convertAlternateBarcodetoItem')->name('convertAlternateBarcodetoItem');
    Route::get('/getJobNum/{job_num?}', 'ApiController@getJobNum')->name('getJobNum');
    Route::get('/getJobOrder/{from_job_num?}', 'ApiController@getJobOrder')->name('getJobOrder');
    Route::get('/getJobNum1/{job_num?}', 'ApiController@getJobNum1')->name('getJobNum1');
    Route::get('/getPoNum/{whse?}/{po_num?}', 'ApiController@getPoNum')->name('getPoNum');
    Route::get('/getPoNumber/{po_num?}', 'ApiController@getPoNumber')->name('getPoNumber');
    Route::get('/getPONumWithoutWhse/{po_num?}', 'ApiController@getPONumWithoutWhse')->name('getPONumWithoutWhse');   // Get PO num list
    Route::get('/getCONumWithoutWhse/{po_num?}', 'ApiController@getCONumWithoutWhse')->name('getCONumWithoutWhse');   // Get PO num list
    Route::get('/getPoNumByPo/{po_num?}', 'ApiController@getPonumByPo')->name('getPonumByPo');
    Route::get('/getPoLine/{po_num}/{po_line?}', 'ApiController@getPoLine')->name('getPoLine');
    Route::get('/getPoRel/{po_num}/{po_line}/{po_rel?}', 'ApiController@getPoRel')->name('getPoRel');
    Route::get('/getItemByPoRel/{po_num}/{po_line}/{po_rel?}', 'ApiController@getItemByPoRel')->name('getItemByPoRel');
    Route::get('/getItemByPoLine/{po_num}/{po_line?}', 'ApiController@getItemByPoLine')->name('getItemByPoLine');
    Route::get('/getItemByCoRel/{co_num}/{co_line}/{co_rel?}', 'ApiController@getItemByCoRel')->name('getItemByCoRel');
    Route::get('/getItemByCoLine/{co_num}/{co_line?}', 'ApiController@getItemByCoLine')->name('getItemByCoLine');
    Route::get('/getItemByCoNum/{whse_num}/{co_num}/{item_num?}', 'ApiController@getItemByCoNum')->name('getItemByCoNum');
    Route::get('/getItemByCoNumPicknShip/{whse_num}/{co_num}/{co_line?}/{item_num?}', 'ApiController@getItemByCoNumPicknShip')->name('getItemByCoNumPicknShip');
    Route::get('/getItemByJobSequence/{job_num}/{oper_num}/{sequence?}', 'ApiController@getItemByJobSequence')->name('getItemByJobSequence');
    Route::get('/getItem/{item_num?}', 'ApiController@getItem')->name('getItem');

    Route::get('/getLotRef/{ref_num?}', 'ApiController@getLotRef')->name('getLotRef');

    Route::get('/getPoNumberOpen/{po_num?}', 'ApiController@getPoNumberOpen')->name('getPoNumberOpen');


    Route::get('/getItemByCoNumCus/{whse_num?}/{cust_num?}', 'ApiController@getItemByCoNumCus')->name('getItemByCoNumCus');
    Route::get('/getItemByWhse/{whse_num?}', 'ApiController@getItemByWhse')->name('getItemByWhse');

    Route::get('/getItemByUom/{uom?}', 'ApiController@getItemByUom')->name('getItemByUom');

    Route::get('/getItemByTO/{whse_num?}/{trn_num?}/{item_num?}', 'ApiController@getItemByTO')->name('getItemByTO');
    Route::get('/getItemWithUOM/{item_num?}', 'ApiController@getItemWithUOM')->name('getItemWithUOM');
    Route::get('/getItemWithUOMAndParent/{item_num?}', 'ApiController@getItemWithUOMAndParent')->name('getItemWithUOMAndParent');
    Route::get('/getManufacturedItem/{item_num?}', 'ApiController@getManufacturedItem')->name('getManufacturedItem');
    Route::get('/getExistingItem/{whse_num}/{item_num?}', 'ApiController@getExistingItem')->name('getExistingItem');

    Route::get('/getExistingItemNoCheck/{whse_num}/{item_num?}', 'ApiController@getExistingItemNoCheck')->name('getExistingItemNoCheck');


    Route::get('/getLotTrackedItem/{item_num?}', 'ApiController@getLotTrackedItem')->name('getLotTrackedItem');
    Route::get('/getPOItem/{whse_num}/{po_num?}/{item_num?}', 'ApiController@getPOItem')->name('getPOItem');
    Route::get('/getPOItemByLine/{whse_num}/{po_num?}/{po_line?}/{item_num?}', 'ApiController@getPOItemByLine')->name('getPOItem');

    Route::get('/getGRNItem/{whse_num}/{grn_num?}/{item_num?}', 'ApiController@getGRNItem')->name('getGRNItem');
    Route::get('/getVendorDO/{whse_num}/{po_num?}/{vend_do?}', 'ApiController@getVendorDO')->name('getVendorDO'); // Get Vendor DO list
    Route::get('/getGRNVendorDO/{whse_num}/{grn_num?}/{vend_do?}', 'ApiController@getGRNVendorDO')->name('getGRNVendorDO'); // Get Vendor DO list for GRN
    Route::get('/getReturnablePonum/{whse}/{po_num?}', 'ApiController@getReturnablePonum')->name('getReturnablePonum');
    Route::get('/getReturnablePoLine/{po_num}/{po_line?}', 'ApiController@getReturnablePoLine')->name('getReturnablePoLine');
    Route::get('/getReturnableGrnNum/{grn_num?}', 'ApiController@getReturnableGrnNum')->name('getReturnableGrnNum');
    Route::get('/getReturnableGrnLine/{grn_num}/{grn_line?}', 'ApiController@getReturnableGrnLine')->name('getReturnableGrnLine');
    Route::get('/getReturnableConum/{co_num?}', 'ApiController@getReturnableConum')->name('getReturnableConum');
    Route::get('/getReturnableCoLine/{co_num}/{co_line?}', 'ApiController@getReturnableCoLine')->name('getReturnableCoLine');
    Route::get('/getAllPoLine/{po_num}/{po_line?}', 'ApiController@getAllPoLine')->name('getAllPoLine');
    Route::get('/getfirstPOByPo/{po_item}', 'ApiController@getfirstPOByPo')->name('getfirstPOByPo');
    Route::get('/getLoc/{whse_num}/{loc_num?}', 'ApiController@getLoc')->name('getLoc');
    Route::get('/getLocPackingValidate/{whse_num}/{loc_num?}', 'ApiController@getLocPackingValidate')->name('getLocPackingValidate');
    Route::get('/getLocPackingValidated', 'ApiController@getLocPackingValidated')->name('getLocPackingValidated');


    Route::get('/getReturnNum/{cust_num}/{return_num?}', 'ApiController@getReturnNum')->name('getReturnNum');

    Route::get('/getCOReturnNum/{return_num?}', 'ApiController@getCOReturnNum')->name('getCOReturnNum');
    Route::get('/getAllCOReturnNum/{return_num?}', 'ApiController@getAllCOReturnNum')->name('getAllCOReturnNum');


    Route::get('/getLocMaster/{whse_num}/{loc_num?}', 'ApiController@getLocMaster')->name('getLocMaster');

    Route::get('/getLocmaster/{loc_num?}', 'ApiController@getLocMaster2')->name('getLocMaster2');

    Route::get('/getLocNoPicking/{whse_num}/{loc_num?}', 'ApiController@getLocNoPicking')->name('getLocNoPicking');



    Route::get('/getLocFilter/{whse_num}/{loc_num?}', 'ApiController@getLocfilterLoc')->name('getLocFilter');

    Route::get('/getLocPL/{whse_num}/{loc_num?}', 'ApiController@getLocPL')->name('getLocPL');
    Route::get('/getLocPacking/{whse_num}/{loc_num?}', 'ApiController@getLocPacking')->name('getLocPacking');
    Route::get('/getPackingList/{whse_num}/{pack_num?}', 'ApiController@getPackingList')->name('getPackingList');
    Route::get('/getLocWithoutWhse/{loc_num?}', 'ApiController@getLocWithoutWhse')->name('getLocWithoutWhse');
    Route::get('/getTransitLoc/{loc_num?}', 'ApiController@getTransitLoc')->name('getTransitLoc');
    Route::get('/getToTransitLoc/{whse_num}/{loc_num?}', 'ApiController@getToTransitLoc')->name('getToTransitLoc');
    Route::get('/getVendor/{vend_num?}', 'ApiController@getVendor')->name('getVendor');
    Route::get('/getItemVendor/{whse_num}/{item_num?}/{vend_num?}', 'ApiController@getItemVendor')->name('getItemVendor');
    Route::get('/getGRNVendor/{whse_num}/{grn_num?}/{item_num?}/{vend_num?}', 'ApiController@getGRNVendor')->name('getGRNVendor');
    Route::get('/getVendorByPo/{po_num}', 'ApiController@getVendorByPo')->name('getVendorByPo');
    Route::get('/getVendorByGrn/{grn_num}', 'ApiController@getVendorByGrn')->name('getVendorByGrn');
    Route::get('/getWorkCenter/{wc_num?}', 'ApiController@getWorkCenter')->name('getWorkCenter');
    Route::get('/getWorkCenterJobRoute/{job_num?}', 'ApiController@getWorkCenterJobRoute')->name('getWorkCenterJobRoute');
    Route::get('/getTrnReceiptNum/{whse}/{trn_num?}', 'ApiController@getTrnReceiptNum')->name('getTrnReceiptNum');
    Route::get('/getTrnNum/{trn_num?}', 'ApiController@getTrnNum')->name('getTrnNum');
    Route::get('/getTrnNumWhse/{from_whse?}/{trn_num?}', 'ApiController@getTrnNumWhse')->name('getTrnNumWhse');   // Get list of open status TO according to selected whse
    Route::get('/getAllTrnNumWhse/{whse_num?}/{trn_num?}', 'ApiController@getAllTrnNumWhse')->name('getAllTrnNumWhse');   // Get list of all TO according to selected whse
    Route::get('/getTrnNumFromTrnLine/{trn_num?}', 'ApiController@getTrnNumFromTrnLine')->name('getTrnNumFromTrnLine');
    Route::get('/getCoNum/{co_num?}', 'ApiController@getCoNum')->name('getCoNum');
    Route::get('/getCoNumLine/{co_num?}', 'ApiController@getCoNumLine')->name('getCoNumLine');

    Route::get('/getCoNumOpen/{co_num?}', 'ApiController@getCoNumOpen')->name('getCoNumOpen');
    Route::get('/getCoNumWhse/{whse_num}/{co_num?}', 'ApiController@getCoNumWhse')->name('getCoNumWhse');
    Route::get('/getFirstCoNum/{co_num?}', 'ApiController@getFirstCoNum')->name('getFirstCoNum');
    Route::get('/getFirstCoLine/{co_num}/{co_line?}', 'ApiController@getFirstCoLine')->name('getFirstCoLine');
    Route::get('/getFirstPoLine/{po_num}/{po_line?}', 'ApiController@getFirstPoLine')->name('getFirstPoLine');
    Route::get('/getFirstGrnLine/{grn_num}/{grn_line?}', 'ApiController@getFirstGrnLine')->name('getFirstGrnLine');
    Route::get('/getAllCoLine/{co_num}/{co_line}', 'ApiController@getAllCoLine')->name('getAllCoLine');
    Route::get('/getOpenCoNum/{whse_num}/{co_num?}', 'ApiController@getOpenCoNum')->name('getOpenCoNum');
    Route::get('/getCoNumPacking/{whse_num}/{co_num?}', 'ApiController@getCoNumPacking')->name('getCoNumPacking');
    Route::get('/getOpenPoNum/{whse_num}/{po_num?}', 'ApiController@getOpenPoNum')->name('getOpenPoNum');
    Route::get('/getOpenPoNo/{whse_num}/{item_num}/{vend_num}/{po_num?}', 'ApiController@getOpenPoNo')->name('getOpenPoNo');
    Route::get('/getCoLine/{co_num}/{whse_num}/{co_line?}', 'ApiController@getCoLine')->name('getCoLine');
    Route::get('/getCoRelease/{co_num}/{co_line}/{co_release?}', 'ApiController@getCoRelease')->name('getCoRelease');
    Route::get('/getStageLoc/{whse}/{loc?}', 'ApiController@getStageLoc')->name('getStageLoc');
    Route::get('/getStagedCo/{whse}/{stageloc}/{co_num?}', 'ApiController@getStagedCo');
    Route::get('/getStagedCoLoc/{whse}/{co_num}/{stageloc?}', 'ApiController@getStagedCoLoc');
    Route::get('/getStagedCoLocPallet/{whse}/{co_num}/{stageloc?}', 'ApiController@getStagedCoLocPallet');
    Route::get('/getTrnLine/{trn_num}/{trnline?}', 'ApiController@getTrnLine')->name('getTrnLine');
    Route::get('/getJobSuffix/{whse_num}/{jobSuffix?}', 'ApiController@getJobSuffix')->name('getJobSuffix');
    Route::get('/getJobQtyCompletedMoreThan0/{whse_num?}/{item_num?}', 'ApiController@getJobQtyCompletedMoreThan0')->name('getJobQtyCompletedMoreThan0');
    Route::get('/getJobAOCJobLabor/{whse_num}/{jobSuffix?}', 'ApiController@getJobAOCJobLabor')->name('getJobAOCJobLabor');
    Route::get('/getJobAOCMachineRun/{whse_num}/{jobSuffix?}', 'ApiController@getJobAOCMachineRun')->name('getJobAOCMachineRun');
    Route::get('/getJobAOCWIPMove/{whse_num}/{jobSuffix?}', 'ApiController@getJobAOCWIPMove')->name('getJobAOCWIPMove');
    Route::get('/getJobItem/{item_num?}', 'ApiController@getJobItem')->name('getJobItem');
    Route::get('/getNonCompletedJobs/{whse_num}/{job_num?}', 'ApiController@getNonCompletedJobs')->name('getNonCompletedJobs');
    Route::get('/getSuffix/{job_num?}', 'ApiController@getSuffix')->name('getSuffix');
    Route::get('/getSuffixJob/{suffix?}', 'ApiController@getSuffixJob')->name('getSuffixJob');

    Route::get('/checkActualStandard/{jobidsufix?}', 'ApiController@checkActualStandard')->name('checkActualStandard');


    Route::get('/getSuffixWithQtyReceivable/{job_num?}', 'ApiController@getSuffixWithQtyReceivable')->name('getSuffixWithQtyReceivable');
    Route::get('/getCompletedJobSuffix/{whse_num?}/{jobSuffix?}', 'ApiController@getCompletedJobSuffix')->name('getCompletedJobSuffix');
    Route::get('/getCompletedJobSuffixSorted/{whse_num?}/{jobSuffix?}', 'ApiController@getCompletedJobSuffixSorted')->name('getCompletedJobSuffixSorted');
    Route::get('/getCompletedJobSuffixSorted_Without/', 'ApiController@getCompletedJobSuffixSorted_Without')->name('getCompletedJobSuffixSorted_Without');

    Route::get('/getCompletedJobSuffixSort/{whse_num?}/{jobSuffix?}', 'ApiController@getCompletedJobSuffixSort')->name('getCompletedJobSuffixSort');
    Route::get('/getCompletedJobOrderSuffix/{whse_num?}/{jobSuffix1?}', 'ApiController@getCompletedJobOrderSuffix')->name('getCompletedJobOrderSuffix');
    Route::get('/getCompletedJobs/{whse_num?}/{jobSuffix?}', 'ApiController@getCompletedJobs')->name('getCompletedJobs');
    Route::get('/getPickList/{pick_num?}', 'ApiController@getPickList')->name('getPickList');
    Route::get('/getPickListPacking/{whse_num?}/{loc_num?}/{pick_num?}', 'ApiController@getPickListPacking')->name('getPickListPacking');
    Route::get('/getWhseLoc/{whse}/{loc_num?}', 'ApiController@getWhseLoc')->name('getWhseLoc');
    Route::get('/getWhseZone/{whse}/{zone_num?}', 'ApiController@getWhseZone')->name('getWhseZone');
    Route::get('/getWhseLocStock/{whse}/{loc_num?}', 'ApiController@getWhseLocStock')->name('getWhseLocStock');
    Route::get('/getMatlJobSuffix/{whse}/{job?}', 'ApiController@getMatlJobSuffix')->name('getMatlJobSuffix');
    Route::get('/getJobMatlIssueOnly/{whse}/{job?}', 'ApiController@getJobMatlIssueOnly')->name('getJobMatlIssueOnly');
    Route::get('/getMatlItem/{item?}', 'ApiController@getMatlItem')->name('getMatlItem');
    Route::get('/getLot/{whse}/{item_num}/{lot_num?}/{no_recomendation?}', 'ApiController@getLot')->name('getLot');

    Route::get('/getLotpreassign/{whse}/{item_num}/{type}/{reference_no}/{reference_line}/{lot_num?}/{no_recomendation?}', 'ApiController@getLotpreassign')->name('getLotpreassign');

    Route::get('/getAllLots/{whse}/{item_num}/{lot_num?}', 'ApiController@getAllLots')->name('getAllLots');

    Route::get('/getLotPallet/{whse}/{item_num}/{loc_num?}/{lot_num?}', 'ApiController@getLotPallet')->name('getLotPallet');


    Route::get('/getAllLot/{lot_num?}', 'ApiController@getAllLot')->name('getAllLot');
    Route::get('/getLocNum/{whse}/{item_num}', 'ApiController@getLocNum')->name('getLocNum');
    Route::get('/getLotNum/{whse?}/{loc?}/{item_num?}', 'ApiController@getLotNum')->name('getLotNum');
    Route::get('/getLotLocExpiry/{whse?}/{loc?}/{item_num?}/{sortField?}/{sortBy?}/{tparm_issue_location?}/{form_name?}', 'ApiController@getLotLocExpiry')->name('getLotLocExpiry');
    Route::get('/getLotNumPallet/{whse?}/{loc?}/{item_num?}/{lpn_num?}/{sortField?}/{sortBy?}', 'ApiController@getLotNumPallet')->name('getLotNumPallet');
    Route::get('/getLotNumOpen/{whse}/{loc}/{item_num}', 'ApiController@getLotNumOpen')->name('getLotNumOpen');

    Route::get('/getGrnNum/{grn_num?}', 'ApiController@getGrnNum')->name('getGrnNum');
    Route::get('/getOpenGrnNum/{whse_num}/{grn_num?}', 'ApiController@getOpenGrnNum')->name('getOpenGrnNum');

    Route::get('/getLotNumAddOn/{whse}/{frmDB_loc}/{check_qty_to_pick}/{picklist_allocate_lot_num}/{item_num}/{loc?}/{sortField?}/{sortBy?}', 'ApiController@getLotNumAddOn')->name('getLotNumAddOn');

    Route::get('/getLotNumInv/{whse}/{loc}/{item_num}', 'ApiController@getLotNumInv')->name('getLotNumInv');
    Route::get('/getExiprydate/{item}/{lot}', 'ApiController@getLotExpiryDate')->name('getLotExpiryDate');

    Route::get('/getReasonCodeCheck/{reason_num}/{reason_class}', 'ApiController@getReasonCodeValidate')->name('getReasonCodeValidate');

    Route::get('/getLotTOnum/{item_num}/{trn_num}/{trn_line}', 'ApiController@getLotToNum')->name('getLotToNum');

    Route::get('/getLotToUnitNum/{item_num}/{trn_num}/{trn_line}', 'ApiController@getLotToUnitNum')->name('getLotToUnitNum');



    Route::get('/getLocLotQtyELNS', 'ApiController@getLocLotQtyELNS')->name('getLocLotQtyELNS');
    Route::get('/getLocLotQtyELNSPL', 'ApiController@getLocLotQtyELNSPL')->name('getLocLotQtyELNSPL');


    Route::get('/getLocItem/{whse_num}/{loc_num}/{item_num?}', 'ApiController@getLocItem')->name('getLocItem');
    Route::get('/getWhseItem/{whse_num}/{item_num?}', 'ApiController@getWhseItem')->name('getWhseItem');
    Route::get('/getItemfromItemLocItemWhse/{whse_num}/{item_num?}', 'ApiController@getItemfromItemLocItemWhse')->name('getItemfromItemLocItemWhse');


    Route::get('/getItemFromItemWhse/{whse_num}/{item_num?}', 'ApiController@getItemFromItemWhse')->name('getItemFromItemWhse');

    Route::get('/getTrnItem/{trn_num}/{whse_num}/{item_num?}', 'ApiController@getTrnItem')->name('getTrnItem');
    Route::get('/getOper/{job_num}/{suffix?}', 'ApiController@getOper')->name('getOper');
    Route::get('/getOperMatlIssueOnly/{job_num}', 'ApiController@getOperMatlIssueOnly')->name('getOperMatlIssueOnly');
    Route::get('/getOperforLabour/{job_num}', 'ApiController@getOperforLabour')->name('getOperforLabour');
    Route::get('/getOperAOCJobLabor/{job_num}', 'ApiController@getOperAOCJobLabor')->name('getOperAOCJobLabor');
    Route::get('/getOperAOCJobLaborSuffix/{job_num}/{suffix}', 'ApiController@getOperAOCJobLaborSuffix')->name('getOperAOCJobLaborSuffix');
    Route::get('/getOperAOCMachineRun/{job_num}', 'ApiController@getOperAOCMachineRun')->name('getOperAOCMachineRun');
    Route::get('/getOperAOCMachineRunSuffix/{job_num}/{suffix}', 'ApiController@getOperAOCMachineRunSuffix')->name('getOperAOCMachineRunSuffix');
    Route::get('/getOperAOCWIPMove/{job_num}', 'ApiController@getOperAOCWIPMove')->name('getOperAOCWIPMove');
    Route::get('/getOperAOCWIPMoveSuffix/{job_num}/{suffix}', 'ApiController@getOperAOCWIPMoveSuffix')->name('getOperAOCWIPMoveSuffix');
    Route::get('/getJobMatlItem/{job_num}/{oper_num}', 'ApiController@getJobMatlItem')->name('getJobMatlItem');
    Route::get('/getJobMatlItemSuffix/{job_num}/{suffix}/{oper_num}', 'ApiController@getJobMatlItemSuffix')->name('getJobMatlItemSuffix');
    Route::get('/getItemLoc/{whse}/{item_num}/{loc_num?}', 'ApiController@getItemLoc')->name('getItemLoc');
    Route::get('/getItemLocation/{whse}/{item_num}/{loc_num?}', 'ApiController@getItemLocation')->name('getItemLocation');
    Route::get('/getItemLocLot/{whse}/{item_num}/{loc_num}/{lot_num?}', 'ApiController@getItemLocLot')->name('getItemLocLot');

    Route::get('/getItemLocLotPallet/{whse_num}/{item_num}/{lot_num?}', 'ApiController@getItemLocLotPallet')->name('getItemLocLotPallet');

    Route::get('/getItemLocOpen/{whse}/{item_num}/{loc_num?}', 'ApiController@getItemLocOpen')->name('getItemLocOpen');

    Route::get('/getItemLocCheckPicking/{whse?}/{item_num?}/{loc_num?}', 'ApiController@getItemLocCheckPicking')->name('getItemLocCheckPicking');
    Route::get('/getIssueLocation/{form_name}/{tparm_issue_location}/{whse?}/{item_num?}/{loc_num?}', 'ApiController@getIssueLocation')->name('getIssueLocation');
    Route::get('/getReceiptLocation/{form_name}/{tparm_receipt_location}/{whse?}/{loc_num?}', 'ApiController@getReceiptLocation')->name('getReceiptLocation');



    Route::get('/getLPNdetails/{co_num}/{item_num}/{co_line}/{lpn_line}/{lpn_num}/{listData?}/{type_mode?}', 'ApiController@getLPNdetails')->name('getLPNdetails');



    Route::get('/getItemLocforPickList/{whse}/{item_num}/{loc_num?}', 'ApiController@getItemLocforPickList')->name('getItemLocforPickList');

    Route::get('/getItemLocforPickListAdd/{whse}/{item_num}/{frmDb_loc}/{check_qty_to_pick}/{loc_num?}', 'ApiController@getItemLocforPickListAdd')->name('getItemLocforPickListAdd');

    Route::get('/getItemLocInv/{whse}/{item_num}/{loc_num?}', 'ApiController@getItemLocInv')->name('getItemLocInv');
    Route::get('/getItemLocByLoc/{item_num}/{loc_num?}/{whse?}', 'ApiController@getItemLocByLoc')->name('getItemLocByLoc');
    Route::get('/getReasonCode/{reason_class}/{reason_num?}', 'ApiController@getReasonCode')->name('getReasonCode');

    Route::get('/getReasonCodeMiscRecipt', 'ApiController@getReasonCodeMiscRecipt')->name('getReasonCodeMiscRecipt');
    Route::get('/getVendDo/{vend_do?}', 'ApiController@getVendDo')->name('getVendDo');
    Route::get('/getBomRevisionStatus', 'ApiController@getBomRevisionStatus')->name('getBomRevisionStatus');
    Route::get('/checkBomItemRevision', 'ApiController@checkBomItemRevision')->name('checkBomItemRevision');
    Route::get('/getProdCode/{prod_code?}', 'ApiController@getProdCode')->name('getProdCode');
    Route::get('/getAddress/{cust_num?}', 'ApiController@getAddress')->name('getAddress');
    Route::get('/getCust/{cust_num?}', 'ApiController@getCustNum')->name('getCust');
    Route::get('/getCustNumPacking/{whse_num}/{loc_num}/{cust_num?}', 'ApiController@getCustNumPacking')->name('getCustNumPacking');
    Route::get('/getCustNumShippingZone/{cust_num?}', 'ApiController@getCustNumShippingZone')->name('getCustNumShippingZone');
    Route::get('/getCustByCo/{co_num?}', 'ApiController@getCustByCo')->name('getCustByCo');
    Route::get('/getCustNumCO', 'ApiController@getCustNumCO')->name('getCustNumCO');
    Route::get('/getLPN/{whse_num}/{loc_num?}', 'ApiController@getLPN')->name('getLPN');
    Route::get('/getLpnForReprint/{ref_num}/{ref_line?}/{type?}', 'ApiController@getLpnForReprint')->name('getLpnForReprint');


    Route::get('/getLPNList/{whse_num?}/{lpn_num?}', 'ApiController@getLPNList')->name('getLPNList');
    Route::get('/getLPNwithLocList/{lpn_num}/{loc_num}', 'ApiController@getLPNwithLocList')->name('getLPNwithLocList');
    Route::get('/getCheckLPNwithLocList/{lpn_num?}/{loc_num}/{item_num}', 'ApiController@getCheckLPNwithLocList')->name('getCheckLPNwithLocList');

    Route::get('/getLPNListNotTransit/{whse_num?}/{lpn_num?}', 'ApiController@getLPNListNotTransit')->name('getLPNListNotTransit');
    Route::get('/getLPNListCoShip/{whse_num}/{lpn_num?}', 'ApiController@getLPNListCoShip')->name('getLPNListCoShip');
    Route::get('/getLPNListCoUnpick/{whse_num}/{loc_num}/{lpn_num?}', 'ApiController@getLPNListCoUnpick')->name('getLPNListCoUnpick');
    Route::get('/getPalletItemList/{lpn_num}', 'ApiController@getPalletItemList')->name('getPalletItemList');
    Route::get('/getPalletItemDetails/{lpn_num}/{item_num}', 'ApiController@getPalletItemDetails')->name('getPalletItemDetails');
    Route::get('/getLPNListRestric/{whse_num?}/{lpn_num?}', 'ApiController@getLPNListRestric')->name('getLPNListRestric');
    Route::get('/getLPNListTOReceipt/{trn_num}/{lpn_num?}', 'ApiController@getLPNListTOReceipt')->name('getLPNListTOReceipt');

    Route::get('/checkLotNumLPN/{item_num?}/{lpn_num?}/{lot_num?}', 'ApiController@checkLotNumLPN')->name('checkLotNumLPN');




    Route::get('/getShippingZone', 'ApiController@getShippingZoneCode')->name('getShippingZone');
    Route::get('/getEmpNum/{emp_num?}', 'ApiController@getEmpNum')->name('getEmpNum');
    Route::get('/getJobRoute/{job_num?}', 'ApiController@getJobRoute')->name('getJobRoute');
    Route::get('/getJobRouteOper/{job_num}/{oper_num?}', 'ApiController@getJobRouteOper')->name('getJobRouteOper');
    Route::get('/getJobRouteOperSeq/{job_num}/{suffix}/{oper_num}/{sequence?}', 'ApiController@getJobRouteOperSeq')->name('getJobRouteOperSeq');
    Route::get('/getCountSheet/{emp_num}', 'ApiController@getCountSheet')->name('getCountSheet');
    Route::get('/getMachine/{res_id?}', 'ApiController@getMachine')->name('getMachine');
    Route::get('/getIndirectTask/{task_name?}', 'ApiController@getIndirectTask')->name('getIndirectTask');
    Route::get('/getOrderNum/{num?}', 'ApiController@getOrderNum')->name('getOrderNum');
    Route::get('/getOrderNumBasedOnType/{check_order_type?}/{num?}', 'ApiController@getOrderNumBasedOnType')->name('getOrderNumBasedOnType');
    Route::get('/getJobData', 'ApiController@getJobData')->name('getJobData');
    Route::get('/getSuffixData', 'ApiController@getSuffixData')->name('getSuffixData');
    Route::get('/getItemLocOrLotLoc', 'ApiController@getItemLocOrLotLoc')->name('getItemLocOrLotLoc');

    Route::get('/getReferenceNo/{trans_type}', 'ApiController@getReferenceNo')->name('getReferenceNo');

    // route to check weight
    Route::get('/check-catch-weight', 'ApiController@checkCatchWeight')->name('checkCatchWeight');

    // Displays
    Route::get('/displayJobItem/{jobsuffix}', 'ApiController@displayJobItem');
    Route::get('/displayQtyItemAvailable/{whse}/{item}/{loc}/{lot?}', 'ApiController@displayQtyItemAvailable')->name('displayQtyItemAvailable');
    Route::get('/displayQuantity/{item}/{whse}/{loc}/{uom_to?}/{cust_num?}/{vend_num?}', 'ApiController@displayQuantity')->name('displayQuantity');
    Route::get('/displayQuantityCoPick/{item}/{whse}/{loc}/{qty_required}/{qty_required_uom}/{cust_num?}/{uom_to?}/{vend_num?}', 'ApiController@displayQuantityCoPick')->name('displayQuantityCoPick');
    Route::get('/displayQuantityCoPickReturn/{item}/{whse}/{loc}/{qty_required}/{qty_required_uom}/{cust_num?}/{uom_to?}/{vend_num?}', 'ApiController@displayQuantityCoPickReturn')->name('displayQuantityCoPickReturn');
    Route::get('/displayQuantityPOReturn/{item}/{whse}/{loc}/{qty_required}/{qty_required_uom}/{cust_num?}/{uom_to?}/{vend_num?}', 'ApiController@displayQuantityPOReturn')->name('displayQuantityPOReturn');

    Route::get('/displayLotQuantityCoPick/{item}/{whse}/{loc}/{lot}/{qty_required}/{qty_required_uom}/{cust_num?}/{uom_to?}/{vend_num?}', 'ApiController@displayLotQuantityCoPick')->name('displayLotQuantityCoPick');
    Route::get('/displayLotLocQuantity/{item}/{whse}/{loc}/{lot?}/{uom_to?}/{cust_num?}/{vend_num?}', 'ApiController@displayLotLocQuantity')->name('displayLotLocQuantity');
    Route::get('/displayQuantityAddOn/{frmDB_loc}/{check_qty_to_pick}/{item}/{whse}/{loc}/{uom_to?}/{cust_num?}/{vend_num?}', 'ApiController@displayQuantityAddOn')->name('displayQuantityAddOn');

    Route::get('/displayLotQuantity/{item}/{whse}/{loc}/{lot}/{uom_to?}/{cust_num?}/{vend_num?}', 'ApiController@displayLotQuantity')->name('displayLotQuantity');
    Route::get('/displayLotQuantityJobIssue/{item}/{whse}/{loc}/{lot}/{uom_to?}/{cust_num?}/{vend_num?}', 'ApiController@displayLotQuantityJobIssue')->name('displayLotQuantityJobIssue');

    Route::get('/displayLotQuantityAddOnCheck/{frmDB_loc}/{check_qty_to_pick}/{picklist_allocate_lot_num}/{item}/{whse}/{loc}/{lot}/{uom_to?}/{cust_num?}/{vend_num?}', 'ApiController@displayLotQuantityAddOnCheck')->name('displayLotQuantityAddOnCheck');

    Route::get('/displayEmployeeName/{emp_num}', 'ApiController@displayEmployeeName')->name('displayEmployeeName');
    Route::get('/displayQuantityConverted/{uom_from}/{item_num}/{qty}/{uom_to}/{cust_num?}/{vend_num?}/{type?}', 'ApiController@displayQuantityConverted')->name('displayQuantityConverted');
    Route::get('/displayCOQuantityConverted/{uom_from}/{item_num}/{qty}/{uom_to}/{cust_num}', 'ApiController@displayCOQuantityConverted')->name('displayCOQuantityConverted');
    Route::get('/displayPOQuantityConverted/{uom_from}/{item_num}/{qty}/{uom_to}/{vend_num}', 'ApiController@displayPOQuantityConverted')->name('displayPOQuantityConverted');
    Route::get('/displayItemDesc/{item}', 'ApiController@displayItemDesc')->name('displayItemDesc');
    Route::get('/displayItemNum/{search}', 'ApiController@displayItemNum')->name('displayItemNum');

    Route::get('/displayLineStaged/{whse}/{stageloc}/{co_num}', 'ApiController@displayLineStaged');
    Route::get('/displayLineStagedwithshippingzonecode/{whse}/{stageloc}/{co_num}/{shipping_zone_code?}/{salesperson?}', 'ApiController@displayLineStagedwithshippingzonecode');
    Route::get('/displayLineStagedwithsalesperson/{whse}/{stageloc}/{co_num}/{salesperson?}', 'ApiController@displayLineStagedwithsalesperson');




    Route::get('/displayLineStagedByCustNum/{whse}/{stageloc}/{cust_num}/{shipping_zone_code?}/{salesperson?}', 'ApiController@displayLineStagedByCustNum');

    Route::get('/displayLineStagedByCustNumNoShipping/{whse}/{stageloc}/{cust_num}/{salesperson?}', 'ApiController@displayLineStagedByCustNumnoshipping');
    Route::get('/displayTrnOrderDetails/{trn_num}/{trn_line}', 'ApiController@displayTrnOrderDetails')->name('displayTrnOrderDetails');
    Route::get('/displayJobMatlItem/{whse}/{job_num}', 'ApiController@displayJobMatlItem')->name('displayJobMatlItem');
    Route::get('/displayJobMatlItemSuffix/{whse}/{job_num}/{suffix}', 'ApiController@displayJobMatlItemSuffix')->name('displayJobMatlItemSuffix');
    Route::get('/displayCustName/{cust_num}', 'MasterMaintenance\CustomerController@displayCustName')->name('displayCustName');
    Route::get('/displayCustNameShipping/{cust_num}', 'MasterMaintenance\CustomerController@displayCustNameShipping')->name('displayCustNameShipping');
    Route::get('/custnameshippingzone', 'MasterMaintenance\CustomerController@getCustNameShippingZone')->name('custnameshippingzone');
    Route::get('/packagetypedetails', 'MasterMaintenance\PackageTypeController@getPackageTypes')->name('packagetypedetails');
    Route::get('/displayProdCodeDesc/{prod_code}', 'ApiController@displayProdCodeDesc')->name('displayProdCodeDesc');
    Route::get('/displayShippingZone/{shipping_zone_code}', 'ApiController@displayShippingZone')->name('displayShippingZone');
    Route::get('/displayUOMDesc/{uom}', 'ApiController@displayUOMDesc')->name('displayUOMDesc');
    Route::get('/displayVendName/{vend_num}', 'MasterMaintenance\VendorController@displayVendName')->name('displayVendName');

    // Get - UOM
    Route::get('/getUOM/{uom_num?}', 'ApiController@getUOM')->name('getUOM');
    Route::get('/getItemUOMConv/{item_num}', 'ApiController@getItemUOMConv')->name('getItemUOMConv');
    Route::get('/getAllUOMConv/{item_num}/{base_uom?}/{cust_num?}/{vend_num?}', 'ApiController@getAllUOMConv')->name('getAllUOMConv');
    Route::get('/getPOUOMConv/{item_num}/{vend_num}', 'ApiController@getPOUOMConv')->name('getPOUOMConv');
    Route::get('/getCOUOMConv/{item_num}/{cust_num}', 'ApiController@getCOUOMConv')->name('getCOUOMConv');
    Route::get('/getCOUOMConvDiffUom/{item_num}/{cust_num}/{base_uom}', 'ApiController@getCOUOMConvDiffUom')->name('getCOUOMConvDiffUom');
    Route::get('/getTOUOMConv/{item_num}/{trn_num}/{trn_line}', 'ApiController@getTOUOMConv')->name('getTOUOMConv');
    Route::get('/getTOLossUOMConv/{item_num}/{trn_num}/{trn_line}/{uom}', 'ApiController@getTOLossUOMConv')->name('getTOLossUOMConv');
    Route::get('/getRelatedUOM/{item_num}/{cust_num}/{base_uom}/{line_uom}', 'ApiController@getRelatedUOM')->name('getRelatedUOM');

    // Get checkContaminationCount
    Route::get('/checkContaminationCount/{jr_lot?}', 'ApiController@checkContaminationCount')->name('checkContaminationCount');
    // Validate
    Route::post('/validateCompanyEmail', 'ValidationController@validateCompanyEmail')->name('validateCompanyEmail');
    Route::post('/validateLoc', 'ValidationController@validateLoc')->name('LocValidation');
    Route::post('/validateItemWhseLoc', 'ValidationController@validateItemWhseLoc')->name('validateItemWhseLoc');
    Route::post('/validateStageLoc', 'ValidationController@validateStageLoc')->name('StageLocValidation');
    Route::post('/validateJobRoute', 'ValidationController@validateJobRoute')->name('JobRouteValidation');
    Route::post('/validateWhseJob', 'ValidationController@validateWhseJob')->name('validateWhseJob');
    Route::post('/validateCoLine', 'ValidationController@validateCoLine')->name('CoLineValidation');
    Route::post('/validateTrnLine', 'ValidationController@validateTrnLine')->name('TrnLineValidation');
    Route::post('/validateNewLot', 'ValidationController@validateNewLot')->name('NewLotValidation');
    Route::get('/validateItemLot', 'ValidationController@validateItemLot')->name('validateItemLot');
    Route::post('/validateExistingLot', 'ValidationController@validateExistingLot')->name('ExistingLotValidation');
    Route::post('/validateUomConv', 'ValidationController@validateUomConv')->name('validateUomConv');
    Route::post('/validateConvUOM', 'ValidationController@validateConvUOM')->name('validateConvUOM');

    Route::get('/validateLoc2', 'ValidationController@validateLoc2')->name('LocValidation2');
    Route::post('/validateLoc3', 'ValidationController@validateLoc3')->name('LocValidation3');
    Route::get('/validateUser', 'ValidationController@validateUserExists')->name('UserValidation');

    Route::post('/validateExistingLotOpen', 'ValidationController@validateExistingLotOpen')->name('ExistingLotValidationOpen');

    Route::post('/getLotNumDef', 'ValidationController@getLotNumDef')->name('getLotNumDef');

    Route::post('/validateExistingLotTOReceipt', 'ValidationController@validateExistingLotTOReceiptUnit')->name('ExistingLotValidationTOReceipt');
    Route::post('/validateExistingSuffixMobile', 'ValidationController@validateExistingSuffixMobile')->name('ExistingSuffixMobile');
    Route::post('/POvalidation', 'Incoming\POReceiveController@POvalidation')->name('POvalidation');
    Route::post('/POLineOpenvalidation', 'Incoming\POReceiveController@POLineOpenvalidation')->name('POLineOpenvalidation');
    Route::post('/validatePoLine', 'ValidationController@validatePoLine')->name('PoLineValidation');
    Route::post('/validatePoReturnLine', 'ValidationController@validatePoReturnLine')->name('PoReturnLineValidation');

    Route::any('/validatePoReturnQty', 'ValidationController@validatePoReturnQty')->name('validatePoReturnQty');

    Route::post('/validatePoRel', 'ValidationController@validatePoRel')->name('PoRelValidation');
    Route::post('/validateCoRel', 'ValidationController@validateCoRel')->name('CoRelValidation');
    Route::post('/validateFrozen', 'ValidationController@validateFrozen')->name('FrozenValidation');
    Route::post('/validateJobScrapReason', 'ValidationController@validateJobScrapReason')->name('JobScrapReasonValidation');
    Route::post('/validateWIPReverseReason', 'ValidationController@validateWIPReverseReason')->name('validateWIPReverseReason');

    Route::post('/validateWarehouseItem', 'ValidationController@validateWarehouseItem')->name('WarehouseItemValidation');
    Route::post('/validateGetItemfromItemLocItemWhse', 'ValidationController@validateGetItemfromItemLocItemWhse')->name('validateGetItemfromItemLocItemWhse');
    // Route::get('/whseValidate/{whse_num}', 'MasterMaintenance\ApiController@whseValidate')->name('whseValidate');
    Route::post('/validateWarehouseItemv2', 'ValidationController@validateWarehouseItemv2')->name('WarehouseItemValidationv2');

    Route::post('/validateWarehouseItemV3', 'ValidationController@validateWarehouseItemV3')->name('validateWarehouseItemV3');

    Route::post('/validateGetItemfromItemWhse', 'ValidationController@validateGetItemfromItemWhse')->name('validateGetItemfromItemWhse');
    Route::post('/validatePOItemWhse', 'ValidationController@validatePOItemWhse')->name('POItemWarehouseValidation');

    Route::post('/reasonValidation', 'ValidationController@reasonValidation')->name('reasonValidation');

    Route::post('/reasonCodeValidation', 'ValidationController@reasonCodeValidation')->name('reasonCodeValidation');

    Route::post('/validateOrderNum', 'ValidationController@validateOrderNum')->name('validateOrderNum');
    Route::post('/validateVendDO', 'ValidationController@validateVendDO')->name('validateVendDO');
    Route::post('/validateOrderItem', 'ValidationController@validateOrderItem')->name('validateOrderItem');
    Route::post('/validateCoNum', 'ValidationController@validateCoNum')->name('validateCoNum');

    Route::post('/validate', 'ValidationController@frontEndValidate')->name('validation');
    Route::post('/validate2', 'ValidationController@frontEndValidate2')->name('validation2');

    Route::post('/inventory_count_validation', 'ValidationController@inventoryCountValidation')->name('inventory_count_validation');


    Route::post('/JobReleasedValidation', 'ValidationController@JobReleasedValidation')->name('JobReleasedValidation');
    Route::post('/validateUser', 'ValidationController@validateUser')->name('validateUser');
    Route::post('/validateUserGroup', 'ValidationController@validateUserGroup')->name('validateUserGroup');
    Route::post('/validateTO', 'MasterMaintenance\TransOrderController@validateTO')->name('validateTO');
    Route::post('/validatePallet', 'MasterMaintenance\PalletController@validatePallet')->name('validatePallet');
    Route::post('/validateJobOrder', 'MasterMaintenance\JobController@validateJobOrder')->name('validateJobOrder');
    Route::post('/validateJobRouteWeb', 'MasterMaintenance\JobRouteController@validateJobRouteWeb')->name('validateJobRouteWeb');
    Route::post('/validateShippingZone', 'MasterMaintenance\ShippingZoneController@validateShippingZone')->name('validateShippingZone');
    Route::post('/validateShippingZoneCO', 'MasterMaintenance\ShippingZoneController@validateShippingZoneCO')->name('validateShippingZoneCO');

    Route::post('/validateReferenceNo', 'ValidationController@validateReferenceNo')->name('validateReferenceNo');

    Route::post('/validateCoReturnNum', 'ValidationController@validateCoReturnNum')->name('validateCoReturnNum');


    Route::post('/validateCOHeader', 'MasterMaintenance\COHeaderController@validateCOHeader')->name('validateCOHeader');
    Route::post('/validatePOHeader', 'MasterMaintenance\POHeaderController@validatePOHeader')->name('validatePOHeader');

    Route::post('/validateCustomer', 'MasterMaintenance\CustomerController@validateCustomer')->name('validateCustomer');

    Route::get('/validateCustomerNum', 'MasterMaintenance\CustomerController@validateCustomerNum')->name('validateCustomerNum');

    Route::get('/validateReturnNumCR', 'MasterMaintenance\CustomerController@validateReturnNumCR')->name('validateReturnNumCR');
    Route::get('/validateWhseNumCR', 'MasterMaintenance\CustomerController@validateWhseNumCR')->name('validateWhseNumCR');
    Route::get('/validateItemNumCR', 'MasterMaintenance\CustomerController@validateItemNumCR')->name('validateItemNumCR');



    // END PROTOTYPE ROUTE//
    // Data API list to use in JSGrid and AJAX calls
    Route::get('/users', 'UserController@ap_index')->name('userapsearch');
    Route::get('/groups', 'GroupController@groupapi')->name('groupapsearch');
    Route::get('/printers', 'PrinterController@ap_index')->name('printerapsearch');
    Route::get('/labels', 'LabelController@ap_index')->name('labelapsearch');
    Route::get('/items', 'MasterMaintenance\ItemController@itemapi')->name('itemapsearch');
    Route::get('/vendors', 'MasterMaintenance\VendorController@vendorapi')->name('vendorapsearch');
    Route::get('/customers', 'MasterMaintenance\CustomerController@custapi')->name('custapsearch');
    Route::get('/warehouses', 'MasterMaintenance\WarehouseController@whseapi')->name('whseapsearch');
    Route::get('/locations', 'MasterMaintenance\LocationController@locapi')->name('locapsearch');
    Route::get('/uoms', 'MasterMaintenance\UOMController@UOMapi')->name('uomapsearch');
    Route::get('/uomsconv', 'MasterMaintenance\UOMConversionController@UOMapi')->name('uomconvapsearch');
    Route::get('/po_num/{po_num?}', 'MasterMaintenance\POController@poapi')->name('poapsearch');
    Route::get('/POlist', 'MasterMaintenance\POHeaderController@list')->name('po.list');
    Route::get('/co_num/{co_num?}', 'MasterMaintenance\COController@coapi')->name('coapsearch');
    Route::get('/job_num', 'MasterMaintenance\JobController@jobapi')->name('jobapsearch');
    Route::get('/jobroute', 'MasterMaintenance\JobRouteController@jobrouteapi')->name('jobroutesearch');
    Route::get('/route/{job_num}', 'MasterMaintenance\JobRouteController@routeapi')->name('routesearch');
    Route::get('/jobmatl', 'MasterMaintenance\JobMatlController@jobmatlapi')->name('jobmatlsearch');
    Route::get('/jobroutematl/{job_num}/{oper_num}', 'MasterMaintenance\JobMatlController@matlapi')->name('matlsearch');
    //Route::get('/trn_num', 'MasterMaintenance\TransOrderController@toapi')->name('tosearch');
    Route::get('/trn_line', 'MasterMaintenance\TransOrderController@toapi')->name('tosearch');
    Route::get('/to_line/{trn_num}', 'MasterMaintenance\TransLineController@toapi')->name('tolinesearch');
    Route::get('/lot_num', 'MasterMaintenance\LotLocationController@lotapi')->name('lotsearch');
    Route::get('/itemlocs', 'MasterMaintenance\ItemLocationController@itemlocapi')->name('itemlocapsearch');
    Route::get('/reasons', 'MasterMaintenance\ReasonCodeController@reasonapi')->name('reasonsearch');
    Route::get('/productcodes', 'MasterMaintenance\ProductCodeController@prodcodeapi')->name('prodcodesearch');
    Route::get('/wc', 'MasterMaintenance\WorkCentreController@wcapi')->name('wcsearch');
    Route::get('/matltrans', 'MasterMaintenance\MaterialTransController@matltransapi')->name('matltransapi');
    Route::get('/matltrans-data', 'MasterMaintenance\MaterialTransController@data')->name('matltrans.data');
    Route::get('/overrideqty', 'MasterMaintenance\OverrideQtyController@api')->name('overrideqtyapi');
    Route::get('/count_sheets', 'MasterMaintenance\CountSheetController@getAllCountSheets')->name('countsheetapisearch');
    Route::get('/count_sheets_line/{countSheet}', 'MasterMaintenance\CountSheetController@getAllCountSheetsLine')->name('countsheetlineapisearch');
    Route::get('/machines', 'MasterMaintenance\MachineController@machineapi')->name('machineapsearch');
    Route::get('/labormachineview', 'MasterMaintenance\LaborMachineController@labormachineapi')->name('labormachineapisearch');
    Route::get('/tasks', 'MasterMaintenance\TaskController@taskapi')->name('tasksearch');
    Route::get('/employees', 'MasterMaintenance\EmployeeController@api')->name('empsearch');
    Route::get('/picklists', 'MasterMaintenance\PicklistController@picklistapi')->name('picklistapisearch');
    Route::get('/inv_batches', 'MasterMaintenance\BatchController@api')->name('batchsearch');
    Route::get('/inv_batches/{batch}', 'MasterMaintenance\CountSheetController@getAllCountSheets')->name('batchlistsearch');
    Route::get('/inv_sheets/{count_sheet}', 'MasterMaintenance\CountSheetController@getAllCountSheetLines')->name('sheetsearch');
    Route::get('/inv_sheets/{count_sheet}/deletables', 'MasterMaintenance\CountSheetController@getDeletableCountSheetLines')->name('deletablesearch');
    Route::get('/inv_sheets/{count_sheet}/exceptions', 'MasterMaintenance\CountSheetController@getExceptionCountSheetLines')->name('exceptionsearch');
    Route::get('/shippingzones', 'MasterMaintenance\ShippingZoneController@shippingzoneapi')->name('shippingzonesearch');

    // Batch delete
    Route::post('/users/delete', 'UserController@ap_delete')->name('userapdelete');
    Route::post('/groups/delete', 'GroupController@ap_delete')->name('groupapdelete');
    Route::post('/label/delete', 'LabelController@ap_delete')->name('labelapdelete');
    Route::post('/items/delete', 'MasterMaintenance\ItemController@ap_delete')->name('itemapdelete');
    Route::post('/vendors/delete', 'MasterMaintenance\VendorController@ap_delete')->name('vendorapdelete');
    Route::post('/customers/delete', 'MasterMaintenance\CustomerController@ap_delete')->name('custapdelete');
    Route::post('/warehouse/delete', 'MasterMaintenance\WarehouseController@ap_delete')->name('whseapdelete');
    Route::post('/locations/delete', 'MasterMaintenance\LocationController@ap_delete')->name('locapdelete');
    Route::post('/uoms/delete', 'MasterMaintenance\UOMController@ap_delete')->name('uomapdelete');
    Route::post('/uomsconv/delete', 'MasterMaintenance\UOMConversionController@ap_delete')->name('uomconvapdelete');
    Route::post('/po_num/delete', 'MasterMaintenance\POController@ap_delete')->name('poapdelete');
    Route::post('/grn_num/delete', 'MasterMaintenance\GRNController@ap_delete')->name('grnapdelete');
    Route::post('/co_num/delete', 'MasterMaintenance\COController@ap_delete')->name('coapdelete');
    Route::post('/co_num/deleteline', 'MasterMaintenance\COController@line_delete')->name('colinedelete');
    Route::post('/job_num/delete', 'MasterMaintenance\JobController@ap_delete')->name('jobapdelete');
    Route::post('/job_route/delete', 'MasterMaintenance\JobRouteController@ap_delete')->name('jobrouteapdelete');
    Route::post('/jobmatl/delete', 'MasterMaintenance\JobMatlController@ap_delete')->name('jobmatlapdelete');
    Route::post('/trn_num/delete', 'MasterMaintenance\TransOrderController@ap_delete')->name('toapdelete');
    Route::post('/to_line/delete', 'MasterMaintenance\TransLineController@ap_delete')->name('tolinesapdelete');
    Route::post('/lot_num/delete', 'MasterMaintenance\LotLocationController@ap_delete')->name('lotapdelete');
    Route::post('/item_locs/delete', 'MasterMaintenance\ItemLocationController@ap_delete')->name('itemlocapdelete');
    Route::post('/productcode/delete', 'MasterMaintenance\ProductCodeController@ap_delete')->name('productcodedelete');
    Route::post('/wc/delete', 'MasterMaintenance\WorkCentreController@ap_delete')->name('wcdelete');
    Route::post('/task/delete', 'MasterMaintenance\TaskController@ap_delete')->name('taskdelete');
    Route::post('/machine/delete', 'MasterMaintenance\MachineController@ap_delete')->name('machinedelete');
    Route::delete('/batch/delete', 'MasterMaintenance\BatchController@ap_delete')->name('batchdelete');
    Route::post('/picklist/delete', 'MasterMaintenance\PicklistController@ap_delete')->name('picklistdelete');
    Route::post('/picklistTest/delete', 'MasterMaintenance\PicklistTestController@ap_delete')->name('picklistTestdelete');
    Route::post('/CountGroup/delete', 'MasterMaintenance\CountGroupController@ap_delete')->name('countgroup.delete');
    Route::post('/customfields/delete', 'Admin\CustomFieldController@ap_delete')->name('customfields.massdelete');
    Route::delete('/boms/delete', 'MasterMaintenance\BomController@ap_delete')->name('bom.massdelete');
    Route::post('/shippingzone/delete', 'MasterMaintenance\ShippingZoneController@ap_delete')->name('shippingzonedelete');
    Route::post('/printer/delete', 'PrinterController@ap_delete')->name('printerdelete');
    Route::post('/pallet/deleteline', 'MasterMaintenance\PalletController@line_delete')->name('palletlinedelete');


    Route::post('/return_num/delete', 'MasterMaintenance\CustomerReturnController@ap_delete')->name('crapdelete');
    Route::post('/return_num/deleteline', 'MasterMaintenance\CustomerReturnController@line_delete')->name('crlinedelete');

    // Validation
    Route::get('/batchv2', 'MasterMaintenance\BatchController@batchv2')->name('batchv2');
    Route::get('/coHeaderExist', 'MasterMaintenance\COHeaderController@coHeaderExist')->name('coHeaderExist');
    Route::get('/whse/{whse_num}', 'MasterMaintenance\WarehouseController@checkWhse')->name('whsev');
    Route::get('/validateWhse/{whse_num}', 'MasterMaintenance\WarehouseController@validateWhse')->name('validateWhse');


    Route::get('/checkLocTransit', 'MasterMaintenance\LocationController@checkLocTransit')->name('checkLocTransit');

    Route::get('/checkLocNotTransit', 'MasterMaintenance\LocationController@checkLocNotTransit')->name('checkLocNotTransit');

    Route::get('/checkLocNotTransitPickingLocation', 'MasterMaintenance\LocationController@checkLocNotTransitpickLocs')->name('checkLocNotTransitpickLocs');

    Route::get('/checkStageLocNotTransit', 'MasterMaintenance\LocationController@checkStageLocNotTransit')->name('checkStageLocNotTransit');
    Route::get('/checkStageLocNotTransitpickLocs', 'MasterMaintenance\LocationController@checkStageLocNotTransitpickLocs')->name('checkStageLocNotTransitpickLocs');
    Route::get('/checkLocCoUnpick', 'MasterMaintenance\LocationController@checkLocCoUnpick')->name('checkLocCoUnpick');
    Route::get('/checkLotNum', 'MasterMaintenance\PalletController@checkLotNum')->name('checkLotNum');

    Route::get('/checkLpnValidation', 'MasterMaintenance\PalletController@checkLpnValidation')->name('checkLpnValidation');
    Route::get('/checkCustRestrict', 'MasterMaintenance\PalletController@checkCustRestrict')->name('checkCustRestrict');
    Route::get('/checkTransitPalletLoc', 'MasterMaintenance\PalletController@checkTransitPalletLoc')->name('checkTransitPalletLoc');

    Route::get('/checkReasonCodeMetaData', 'ApiController@checkReasonCodeMetaData')->name('checkReasonCodeMetaData');

    Route::get('/reasonv2', 'MasterMaintenance\ReasonCodeController@reasonv2')->name('reasonv2');       // Validate reason code
    Route::get('/countGroupv2/{count_group_name}', 'MasterMaintenance\CountGroupController@countGroupv2')->name('countGroupv2');       // Validate count group
    Route::get('/pov2', 'MasterMaintenance\POHeaderController@pov2')->name('pov2');       // Validate po number
    Route::get('/grnv2', 'MasterMaintenance\GRNHeaderController@grnv2')->name('grnv2');       // Validate GRN Number
    //Enabled it after Issue #552, before that it was disabled for "Fix#49,51,52"
    Route::get('/barcode-admin/labels/check/{label_name}', 'LabelController@checkLabelName')->name('labelNamev');
    Route::get('/loc/{loc_num}', 'MasterMaintenance\LocationController@checkLoc')->name('locv_check');
    Route::get('/vendor/{id}', 'MasterMaintenance\VendorController@exist')->name('vendorv');
    Route::get('/vendorv2', 'MasterMaintenance\VendorController@vendorv2')->name('vendorv2');
    Route::get('/cust/{id}', 'MasterMaintenance\CustomerController@exist')->name('custv');
    Route::get('/custv2', 'MasterMaintenance\CustomerController@custv2')->name('custv2');
    Route::get('/shippingzone', 'MasterMaintenance\CustomerController@shippingzone')->name('shippingzone');

    Route::get('/shippingzonecode', 'MasterMaintenance\CustomerController@shippingzonecode')->name('shippingzonecode');
    Route::get('/packagetype', 'MasterMaintenance\PackageTypeController@packagetype')->name('packagetype');
    Route::get('/itemv/{id}', 'MasterMaintenance\ItemController@exist')->name('itemv');
    Route::get('/lotv2', 'ApiController@lotv2')->name('lotv2');
    Route::get('/itemv2', 'ApiController@itemv2')->name('itemv2');
    Route::get('/itemValidationCommon', 'ApiController@itemValidationCommon')->name('itemValidationCommon');
    Route::get('/itemwhse_pallet', 'ApiController@itemwhse_pallet')->name('itemwhse_pallet');
    Route::get('/lotnum_pallet', 'ApiController@lotnum_pallet')->name('lotnum_pallet');
    Route::get('/max_qty_contained', 'ApiController@max_qty_contained')->name('max_qty_contained');
    Route::get('/itemv3', 'ApiController@itemv3')->name('itemv3');
    Route::get('/whsev2', 'ApiController@whsev2')->name('whsev2');
    Route::get('/suffixv', 'ApiController@suffixv')->name('suffixv');
    Route::get('/itemValidation', 'ApiController@itemvalidation')->name('itemvalidation');

    Route::get('/itemvalidation2', 'ApiController@itemvalidation2')->name('itemvalidation2');

    Route::get('/itemValidationWithData', 'MasterMaintenance\ItemController@itemValidationWithData')->name('itemValidationWithData');
    Route::get('/totalQtyOnHand', 'MasterMaintenance\ItemWarehouseController@getTotalQtyOnHand')->name('totalQtyOnHand');
    Route::post('/itemwhsev', 'MasterMaintenance\ItemWarehouseController@itemwhsev')->name('itemwhsev');
    Route::get('/pickerRank', 'MasterMaintenance\PickerMaintenanceController@pickerRank')->name('pickerRank');
    Route::get('/itemlotv', 'MasterMaintenance\LotController@itemlot_exist')->name('itemlotv');
    Route::get('/lotexpiryv', 'MasterMaintenance\LotController@lot_expiry_date_exist')->name('lot_expiry_date_exist');
    Route::get('/lotmfgdate', 'MasterMaintenance\LotController@lot_mfg_date')->name('lot_mfg_date');

    Route::post('/productCodeValidation', 'ApiController@productCodeValidation')->name('productCodeValidation');
    Route::post('/customerValidation', 'ApiController@customerValidation')->name('customerValidation');

    Route::get('/lotitemexpiryv', 'MasterMaintenance\LotController@lot_item_expiry_date_exist')->name('lot_item_expiry_date_exist');
    Route::get('/lotv', 'MasterMaintenance\LotController@lot_exist')->name('lotv');
    Route::get('/item_expiry_tracked/{id}', 'MasterMaintenance\ItemController@expiry_tracked')->name('item.expiry_tracked');
    Route::get('/uomv', 'MasterMaintenance\UOMController@exist')->name('uomv');
    Route::get('/uomv2', 'MasterMaintenance\UOMController@uomv2')->name('uomv2');
    Route::get('/locv/{whse}/{id}', 'MasterMaintenance\LocationController@exist')->name('locv');
    Route::get('/displayLocType/{whse_num}/{loc_num}', 'MasterMaintenance\LocationController@displayLocType')->name('displayLocType');
    Route::get('/locv2', 'MasterMaintenance\LocationController@locv2')->name('locv2');
    Route::get('/trnlocv', 'MasterMaintenance\LocationController@trnexist')->name('trnlocv');
    Route::get('/uomv/{id}', 'MasterMaintenance\UOMController@exist')->name('uomv');
    Route::get('/employeev/{id}', 'MasterMaintenance\EmployeeController@exist')->name('employeev');
    Route::get('/prodcodev/{id}', 'MasterMaintenance\ProductCodeController@exist')->name('prodcodev');
    Route::get('/prodcodev2', 'MasterMaintenance\ProductCodeController@prodcodev2')->name('prodcodev2');
    Route::get('/wcv/{id}', 'MasterMaintenance\WorkCentreController@exist')->name('wcv');
    Route::get('/wcv2', 'MasterMaintenance\WorkCentreController@wcv2')->name('wcv2');

    Route::get('/uomexists', 'MasterMaintenance\UOMController@uomexists')->name('uomexists');

    Route::post('/validateWC', 'MasterMaintenance\WorkCentreController@validateWC')->name('validateWC');




    Route::get('/reasonv/{reason_class}/{reason_num}', 'MasterMaintenance\ReasonCodeController@exist')->name('reasonv');
    Route::get('/itemlocv/{item_num}/{loc_num}/{whse_num}', 'MasterMaintenance\ItemLocationController@exist')->name('itemlocv');

    Route::get('/checkitemwhsev/{item_num}/{loc_num}/{whse_num}', 'MasterMaintenance\ItemLocationController@checkitemwhsev')->name('checkitemwhsev');

    Route::get('/checklocmaster/{loc_num}', 'MasterMaintenance\ItemLocationController@checklocmaster')->name('checklocmaster');


    Route::get('/itemlocv2', 'MasterMaintenance\ItemLocationController@itemlocv2')->name('itemlocv2');
    Route::get('/checkJobItemLoc/{job_num}/{loc_num}/{whse_num}', 'MasterMaintenance\ItemLocationController@checkJobItemLoc')->name('checkJobItemLoc');    // Check job number and location
    Route::get('/lotlocv/{lot_num}/{item_num}/{loc_num}/{whse_num}', 'MasterMaintenance\LotLocationController@exist')->name('lotlocv');
    Route::get('/lotlocv2/{lot_num}/{item_num}/{loc_num}/{whse_num}', 'MasterMaintenance\LotLocationController@existv2')->name('lotlocv2');
    Route::get('/lotitemv/{lot_num}/{item_num}/{whse_num}', 'MasterMaintenance\LotLocationController@lotexist')->name('lotitemv');
    Route::get('/checkJobItemLot/{lot_num}/{job_num}/{whse_num}', 'MasterMaintenance\LotLocationController@checkJobItemLot')->name('checkJobItemLot');  // Check job num and lot location
    Route::get('/lotitemv/{lot_num}/{item_num}/{whse_num}/{loc_num}', 'MasterMaintenance\LotLocationController@lot_expiry_date_exist')->name('lotlocation.lot_expiry_date_exist');
    Route::get('/poitemv/{po_num}/{po_line}/{po_rel}', 'MasterMaintenance\POController@exist')->name('poitemv');
    Route::get('/coitemv/{co_num}/{co_line}/{co_rel}', 'MasterMaintenance\COController@exist')->name('coitemv');
    Route::get('/coitemDetail', 'MasterMaintenance\COController@coitemDetail')->name('coitemDetail');
    Route::get('/transnumv/{id}', 'MasterMaintenance\TransOrderController@exist')->name('transnumv');
    Route::get('/translinev/{trn_num}/{trn_line}', 'MasterMaintenance\TransLineController@exist')->name('translinev');
    Route::get('/jobnumv/{job_num}', 'MasterMaintenance\JobController@exist')->name('jobnumv');
    Route::get('/jobnumberv/{job_num}/{whse_num}', 'MasterMaintenance\JobController@jobexist')->name('jobnumberv');
    Route::get('/jobdetail/{job_num}/{suffix}', 'MasterMaintenance\JobController@jobdetail')->name('jobdetail');


    Route::get('/jobroutev/{job_num}/{oper_num}', 'MasterMaintenance\JobRouteController@exist')->name('jobroutev');
    Route::get('/jobroutevsuffix/{job_num}/{suffix}/{oper_num}', 'MasterMaintenance\JobRouteController@existSuffix')->name('jobroutevsuffix');

    Route::get('/jobroutevsuf/{job_num}/{suffix}/{oper_num}', 'MasterMaintenance\JobRouteController@exist')->name('jobroutevsuf');
    Route::get('/jobmatlv/{job_num}/{oper_num}/{sequence}', 'MasterMaintenance\JobMatlController@exist')->name('jobmatlv');
    Route::get('/jobmatlvsuffix/{job_num}/{suffix}/{oper_num}/{sequence}', 'MasterMaintenance\JobMatlController@existSuffix')->name('jobmatlvsuffix');
    Route::get('/empv/{id}', 'MasterMaintenance\EmployeeController@exist')->name('empv');
    Route::get('/empv2', 'MasterMaintenance\EmployeeController@empv2')->name('empv2');
    Route::get('/taskv/{id}', 'MasterMaintenance\TaskController@exist')->name('taskv');
    Route::get('/machine/{id}', 'MasterMaintenance\MachineController@exist')->name('machinev');
    Route::get('/filev/{type}/{doc}/{ref_num}/{ref_line?}/{ref_release?}', 'DocNoteController@exist')->name('docv');
    Route::get('/rankv/{whse}/{item}/{rank}/{id?}', 'MasterMaintenance\ItemLocationController@rankexist')->name('rankv');
    Route::get('/ranktransitv/{whse}/{rank}/{loc}', 'MasterMaintenance\ItemLocationController@checkranktransit')->name('ranktransitv');
    Route::get('/checkjobduplicate/{emp}/{type}/{whse}/{job}/{oper}', 'LabourReportingController@checkduplicate')->name('jobinfov');
    Route::get('/matlv', 'MasterMaintenance\JobMatlController@valid')->name('matlv');
    Route::get('/baseuom/{item}/{um?}/{cust?}/{vend?}', 'MasterMaintenance\UOMConversionController@getbaseuom')->name('getbaseuom');
    Route::get('/PO/{po_num}/exists', 'MasterMaintenance\POHeaderController@exist')->name('po.exists');
    Route::get('/CO/{co_num}/exists', 'MasterMaintenance\POHeaderController@exist')->name('co.exists');
    Route::get('/shippingzonev/{id}', 'MasterMaintenance\ShippingZoneController@exist')->name('shippingzonev');
    Route::get('/shippingzonev2', 'MasterMaintenance\ShippingZoneController@shippingzonev2')->name('shippingzonev2');
    Route::get('/lotexistpallet/{item_num}/{whse_num}/{loc_num?}/{lot_num}', 'MasterMaintenance\LotLocationController@lotexistpallet')->name('lotexistpallet');

    // UOM Validation
    Route::get('/checkuomv/{uom_from}/{uom_to}', 'MasterMaintenance\UOMConversionController@check')->name('checkuomv');
    Route::get('/convtypev/{uom_from}/{uom_to}/{conv_type}', 'MasterMaintenance\UOMConversionController@exist')->name('convtypev');
    Route::get('/checkuomglobal/{uom_from}/{uom_to}', 'MasterMaintenance\UOMConversionController@checkglobal')->name('checkuomglobal');
    Route::get('/checkuomitem/{uom_from}/{uom_to}/{conv_type}/{item_num}', 'MasterMaintenance\UOMConversionController@checkitem')->name('checkuomitem');
    Route::get('/checkuomvendor/{uom_from}/{uom_to}/{item_num}/{vend_num}/{id}', 'MasterMaintenance\UOMConversionController@checkvendor')->name('checkuomvendor');
    Route::get('/checkuomcust/{uom_from}/{uom_to}/{item_num}/{cust_num}/{id}', 'MasterMaintenance\UOMConversionController@checkcustomer')->name('checkuomcust');
    Route::get('/uomitemv/{uom_from}/{uom_to}/{conv_type}/{item_num}/{id?}', 'MasterMaintenance\UOMConversionController@itemexist')->name('uomitemv');
    Route::get('/convertToBase/{uom}/{qty}/{item_num}/{cust_num?}/{vend_num?}', 'MasterMaintenance\UOMConversionController@convertToBase')->name('convert.base');

    Route::get('/nextrank/{whse}/{item}/{loc}', 'ApiController@getnextRank')->name('nextrank');
    Route::get('/nextrankv2', 'MasterMaintenance\ItemLocationController@nextrankv2')->name('nextrankv2');
    Route::get('/getLocByRankIssue/{whse}/{item}', 'ApiController@getLocByRankIssueBlade')->name('getLocByRankIssue');
    Route::get('/getLocByRankReceipt/{whse}/{item}', 'ApiController@getLocByRankReceiptBlade')->name('getLocByRankReceipt');

    Route::get('/getLocByRankIssuePL/{whse}/{item}', 'ApiController@getLocByRankIssuePL')->name('getLocByRankIssuePL');


    Route::get('/getLocByRankIssueCheck/{whse}/{item}/{itemhidden}', 'ApiController@getLocByRankIssueBladeCheck')->name('getLocByRankIssueCheck');
    Route::get('/getLocByRankReceiptCheck/{whse}/{item}/{itemhidden}', 'ApiController@getLocByRankReceiptBladeCheck')->name('getLocByRankReceiptCheck');
    Route::get('/nextTransaction', 'RouteController@BackButton')->name('backbutton');

    // LPN Validation
    Route::get('/lpnnumv/{lpn_num}/{whse_num}/{loc_num}', 'MasterMaintenance\PalletController@lpnexist')->name('lpnnumv');
    Route::get('/getPalletDetails/{lpn_num}/{whse_num?}', 'MasterMaintenance\PalletController@getPalletDetails')->name('getPalletDetails');
    Route::get('/checkPalletDetails/{whse_num}/{lpn_num}', 'MasterMaintenance\PalletController@checkPalletDetails')->name('checkPalletDetails');
    Route::get('/checkPalletCODetails/{whse_num}/{lpn_num}/{co_num}', 'MasterMaintenance\PalletController@checkPalletCODetails')->name('checkPalletCODetails');
    Route::get('/checkPalletTODetails/{whse_num}/{lpn_num}/{trn_num}', 'MasterMaintenance\PalletController@checkPalletTODetails')->name('checkPalletTODetails');
    // Route::get('/getLPNwithLocList/{lpn_num?}/{loc_num}', 'MasterMaintenance\PalletController@getLPNwithLocList')->name('getLPNwithLocList');
    Route::get('/getPalletItem/{lpn_num}', 'MasterMaintenance\PalletController@getPalletItem')->name('getPalletItem');
    Route::get('/getPalletDetailsRestric/{lpn_num}/{whse_num?}', 'MasterMaintenance\PalletController@getPalletDetailsRestric')->name('getPalletDetailsRestric');
    Route::get('/getPalletSingleItem/{lpn_num}/{item_num}', 'MasterMaintenance\PalletController@getPalletSingleItem')->name('getPalletSingleItem');
    Route::get('/displayLpnItemCount/{lpn_num}', 'ApiController@displayLpnItemCount')->name('displayLpnItemCount');
    Route::get('/displayLpnSource/{lpn_num}', 'ApiController@displayLpnSource')->name('displayLpnSource');
    Route::get('/checkLocLpn/{whse_num}/{loc_num}', 'ApiController@checkLocLpn')->name('checkLocLpn');
    Route::get('/checkLpn', 'ApiController@checkLpn')->name('checkLpn');
    Route::get('/checkPalletItem', 'ApiController@checkPalletItem')->name('checkPalletItem');
    Route::get('/getItemList/{item_num?}', 'ApiController@getItemList')->name('getItemList');
    Route::get('/getQtyLimit/{baseuom?}/{qty?}/{selectuom?}/{item_num?}/{whse_num?}/{loc_num?}/{lot_num?}/{ori_uom?}/{cust_num?}/{vend_num?}/{form_type?}', 'ApiController@getConvQtyLimit')->name('getConvQtyLimit');

    Route::get('/getShippingZone/search', 'ApiController@getShippingZoneSearch')->name('getShippingZoneSearch');
    Route::get('/getZone/search', 'ApiController@getZoneSearch')->name('getZoneSearch');
    Route::get('/getLocation/search', 'ApiController@getLocationSearch')->name('getLocationSearch');

    // Bundle Builder Validation
    Route::get('/checkItemLot', 'ApiController@checkItemLot')->name('checkItemLot');
    Route::get('/checkReels', 'ApiController@checkReels')->name('checkReels');

    Route::get('/getAllBundleList/{item_num}', 'ApiController@getAllBundleList')->name('getAllBundleList');
    Route::get('/checkReelsExist', 'ApiController@checkReelsExist')->name('checkReelsExist');

    Route::get('/checkCounter/{counter?}', 'ApiController@getCheckCount')->name('checkcounter');

    Route::get('/checkReasonCodeValidation', 'ApiController@checkReasonCodeValidation')->name('checkReasonCodeValidation');

    Route::get('/checkSAPConnnection', 'ApiController@checkSAPConnnection')->name('checkSAPConnnection');

    // Health Check UI route
    Route::get('/health', [Spatie\Health\Http\Controllers\HealthCheckResultsController::class, '__invoke']);
}); //end of auth Route::group
Route::get('/test/reset', 'TestController@index')->name('test');
Route::get('/test/resetAll', 'TestController@resetAll')->name('resetAll');

// route to validate user password for guest
Route::post('/validateUserPassword', [App\Http\Controllers\Auth\ResetPasswordController::class, 'validateUserPassword'])->name('validateUserPassword');

 // Health Check Connection
 Route::get('/healthCheckConnection/{connection}','HealthCheckConnectionController@healthCheckConnections')->name('healthCheckConnection');

if (env('APP_ENV') == "production") {
    URL::forceScheme('https');
}
