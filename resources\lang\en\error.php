<?php

return [
    'mobile' => [
        'lessthanqtyreturned' => ':resource1 cannot less than  :resource2',
        'batch_job_whse' => "Please specify the default issue location for this warehouse",
        'inactive_loc' => 'Loc is inactive.',
        'inactive_loc2' => ':resource is inactive.',
        // 'qty_issue_more_qty_available'      => 'Qty Issue cannot more than Qty Available.',
        // 'qty_move_more_qty_available'       => 'Qty Move cannot more than Qty Available.',
        // 'qty_putaway_more_qty_available'    => 'Qty Put Away cannot more than Qty Available.',
        // 'qty_coreturn_more_qty_available'   => 'Qty to Return cannot more than Qty Retrunable.',
        // 'qty_custreturn_more_qty_available' => 'Qty to Return cannot more than Qty Req.',
        // 'stageLoc_finish_unpicked'          => 'This co num no longer exists in unpicked lists.',
        // 'qty_unpick_more_qty_picked'        => 'Qty to Unpick cannot more than Qty Picked.',

        'qty_issue_more_qty_available'      => 'Qty must be less than or equal to Qty Available.',
        'qty_move_more_qty_available'       => 'Qty must be less than or equal to Qty Available.',
        'qty_putaway_more_qty_available'    => 'Qty must be less than or equal to Qty Available',
        'qty_coreturn_more_qty_available'   => 'Qty must be less than or equal to Qty Retrunable.',
        'qty_custreturn_more_qty_available' => 'Qty must be less than or equal to Qty Required.',
        'stageLoc_finish_unpicked'          => 'Qty must be less than or equal to Qty Available [0.00000000].',
        'qty_unpick_more_qty_picked'        => 'Qty must be less than or equal to Qty Available',
        'co_pick_qty_qty_available'         => 'Qty must be less than or equal to Qty Available',
        'qty_move_more_qty_picked'          => 'Qty must be less than or equal to Qty Available',
        'qty_notmoreorequalqty_available'   => 'Qty must be less than or equal to Qty Available [:resource_qty_available].',
        'qty_notmoreorequalqty_balance'    => 'Qty must be less than or equal to Qty Balance [:resource_qty_available].',
        'qty_notmoreorequalqtypallet_available'   => 'Qty must be less than or equal to Qty Contained [:resource_qty_available].',
        'qty_notmoreorequalqty_return'   => 'Qty must be less than or equal to Qty Returnable [:resource_qty_available].',
        'qty_notmoreorequalqty_required'   => 'Qty must be less than or equal to Qty Required [:resource_qty_available].',
        'qty_notmoreorequalqty_topick'  => 'Qty must be less than or equal to Qty to Pick [:resource_qty_available].',
        'qty_notmoreorequalqty_picked'  => 'Qty must be less than or equal to Qty Picked [:resource_qty_available].',
        'atleastone' => 'Please select at least one record.',
        'Warning!' => 'Warning',
        'Error' => 'Error',
        'emptytemplate' => 'Please select a label template.',
        'jobnotmatch' => ':resource does grn_line.',
        'notfound' => ':resource :name not found.',
        'notexist' => ':resource does not exist.',
        'notexist2' => ':resource1 Failed: The :model [:resource2] no longer exists.',
        'notexist3' => ':resource [:resource2] does not exist.',
        'notexistLoc' => 'Location does not exist.',
        'itemlocnotexist' => ':resource does not exist in the location.',
        'itemwhsenotexist' => 'Item does not exist in the warehouse.', // :resource
        'itemnotexist' => 'Item does not exist.',
        'inactive' => ':resource [:name] is inactive.',
        'exists' => ':resource :name already exists.',
        'exists_line' => ':resource already exists.',
        'exists_in' => ':resource1 exists in :resource2.',
        'not_exists_in' => ':resource1 not exists in :resource2.',
        'same' => ':resource1 and :resource2 must be different.',
        'lessthanother' => ':resource1 must be less than or equal to :resource2.',
        'lessthanotherforline' => ':resource1 must be less than or equal to :resource2 for Line :value',
        'equalto_other' => ':resource1 must be equal to :resource2.',
        'recordnotfound' => 'Record not found.',
        'norecord' => 'Record not found.',
        'lpn_notexists' => 'LPN does not exist.',
        'To_lpn_notexists' => 'To LPN does not exist.',
        'lpn_exists' => 'LPN exist.',
        'lpn_null' => 'LPN can not be empty.',
        'lot_notexists' => 'Lot does not exist.',
        'pallet_same_item' => 'LPN is restricted to single item only. Receive a different item in this Pallet is not allowed.',
        'cust_restrict' => 'LPN is restricted for customer only. Receive an item in this Pallet is not allowed.',
        'transit_loc' => 'Not allow to receive item into Transit Location.',
        'only_transit_loc' => 'Only can receive from Transit Location.',
        'transit_loc_ship' => 'Not allow to ship from Transit Location.',
        'nomatchingrecord' => 'No matching records found.',
        'required' => ':resource is required.',
        'numbersonly' => ':resource must be a number.',
        'notmatch' => 'Scanned :resource does grn_line.',
        'not_match' => ':resource does grn_line.',
        'not_item_match' => ':resource does not match.',
        'lessthan' => ':resource must be less than or equal to ',
        'exceed' => ':resource [:qty:] exceeds Qty Available [:resource2].',
        'def_loc_not_in_issue_loc' => 'The Default Issue Location must be listed within the Issue Location.',
        'def_loc_not_in_receipt_loc' => 'The Default Receipt Location must be listed within the Receipt Location.',

        'morethan' => ':resource must be more than ',
        'cannotlessthan' => ':resource cannot be less than ',
        'morethanequal' => ':resource must be more than or equal to ',
        'equal_to' => ':resource must be equal to ',
        'error_return_qty_greater_than_required' => 'Qty to Return must be less than or equal to Qty Required.',
        'frozen' => 'Item at this location is frozen.',
        'mustdiffer' => ':resource1 and :resource2 must be different.',
        'notenough' => 'Qty on Hand is not enough.',
        'notenough_for' => 'Qty on Hand is not enough for :resource1.',
        'notenough_qty_available_for' => 'Qty available is not enough for :resource1.',
        'no_qty_available' => 'No qty available.',
        'qty_pick_notequal' => 'Qty must be equal to ',
        'processed' => ':process processed unsuccessfully.',
        'insufficient_qty_available' => 'Insufficient Qty Available.',
        'insufficient_qty_available_pick' => 'Qty to Pick cannot more than Qty Available.',
        'insufficient_qty_issued' => 'Insufficient Qty Issued for the Material.',
        'insufficient' => 'Insufficient quantity to issue.',
        'duplicatejob' => 'Cannot run the same Job Type twice for the same Job Order and Employee. Please end the earlier Job Type transaction first.',
        'duplicatetask' => 'Cannot run the same Job Type twice for the same Employee. Please end the earlier Job Type transaction first.',
        'duplicate_task' => ':resource1 is already running :resource2. Please end the existing :resource3 transaction first.',
        'duplicate_task1' => ':resource1 is already running :resource2 for :resource3. Please end the existing :resource4 transaction first.',
        'invaliduom' => 'UOM does not exist in UOM Conversion.',
        'processinactive' => 'Cannot process inactive :resource.',
        'item_expired' => 'Item :resource is expired on :resource2.',
        'lot_expired' => 'Lot :resource is expired on :resource2.',
        'ismorethan' => ':resource1 is more than :resource2.',
        'mustequal' => ':resource1 must equal to :resource2.',
        'no_transit_loc' => ':resource has no transit location.',
        'no_picking_location' => 'Transaction to/from a picking location is not allowed',
        'loc_not_exists' => 'Loc does not exist.',
        'max_characters' => 'Max characters reached',
        'same_location'  => 'Loc cannot same with To Loc',
        'same_stage_location'  => 'Loc cannot same with Stage Loc',
        'same_stage_location_with_value'  => ':resource1 [:value1] cannot same with :resource2 [:value2]',
        'no_of_pallet_qty' => 'Total quantity to receive cannot more than quantity required. ',
        'lpn_no_line' => 'LPN [ :resource1 ] did not have any lines. ',
        'pick_unit_error' => 'Incompatible picking method. Order has unit-picked lines. Choose \'Pick By Unit\' for the entire order.',
        'pick_pallet_error' => 'Incompatible picking method. Order has pallet-picked lines. Choose \'Pick By Pallet\' for the entire order.',
        'loc_freeze' => 'Item [:resource] at Loc [:resource2] is frozen.',
        'restart_run' => ":resource is already running another job operation. Running multiple job operations is not allowed. Restart is not permitted.",

        'no_active_lpn' => 'LPN no active',
        'sap_error'     => 'SAP Error',
        'sap_error_contact' => 'Please contact Site Owner to resolve. ',
        'sap_error_connection' => 'Connection to SAP cannot be established.',
        'sap_server_down' => 'Integration service is currently unavailable. Please try again later or contact IT support.',
        'system_error'       => 'System Error',
        'sys_error_token_missing' => 'System Token being registered and invalid token.',
        'loc_not_defined' => 'Location not defined in parameter.',

        'sap_error_goods_return_request_notexist' => 'Goods Return Request does not exist in SAP.',
        'sap_error_connection_1' => 'SAP connection lost. Sync paused.',
        'sap_error_connection_2' => 'Invalid Username or Password to SAP connection lost. Sync paused.',
        'lpn_not_defined' => 'LPN Definition not defined.',
        'lpn_inactive' => 'LPN Definition is currently disabled.',
        'uom_conv_not_exists' => 'UOM Conversion that has [Base/Converted UOM: :resource1 and :resource2] does not exist.',
        'duplicate_trans'     => 'Duplicate transaction detected.',
        'duplicate_lot_num'     => 'Duplicate Lot Number.',
        'lpn_letdown_validate' => 'Insufficient Quantity (Item [:resource1]: Qty Contained [ :resource2 ], Qty To Letdown: [ :resource3 ])',
        'lpn_moved_loc' => 'This pallet has been moved to location [:resource1]',
        'lpn_destructed' => 'This pallet has been destructed.',
        'insufficient_qty' => 'Insufficient Quantity (Item [:resource1]: Qty Contained [:resource2], Qty To Transfer: [:resource3])',
        'lpn_builder_validation' => 'Insufficient Quantity Available [:resource]',
        'validate_transit' => 'Transit Location not allowed.',
        'validate_picking' => 'Picking Location not allowed.',
        'lpn_noexists'     => 'LPN does not exists',
        'lpn_line_noexists'     => 'LPN does not have lines',
        'lpn_qty_contained_exceed_qty_avaible' => 'Quantity contained in the pallet does not exceed the quantity available.',
        'vendor_diff' => 'This Doc Number exists and belongs to another Vendor. You cannot use the same Doc Number for different Vendor.',
        'status_is_completed' => ':resource cannot be proceed due to status is completed/closed',
        'resource_completed' => ':resource status is Completed',
        //'shippingzon_salesperson' => ' Shipping zone /  Salesperson does grn_line with CO',
        'shippingzon_salesperson' => 'No record found',
        'zoho_error' => 'Zoho Error',
        'zoho_error_token_missing' => 'Unable to connect to the billing service. Please try again later or contact support if the issue persists.',
        'does_not_exists' => ':resource does not exists',
    ],
    'admin' => [
        // A

        'added_by' => ':resource1 has already been added by :resource2',
        'atleastone' => 'Please select at least one record.',
        'atleastoneresource' => 'Please select at least one :resource.',
        'alpha_dash' => 'The phone number may only contain letters, numbers, dashes and underscores.',
        'atleastonerefnum' => 'Please select at least one Ref Num.',
        // B
        'batch_id_exists' => 'Batch ID Already exists.',

        'batch_closed' => 'Batch status is Closed.',
        'bundle_exist' => 'Reels [:reels] for Bundle Lot [:bundle_lot] and item [:item_num] is already exist.',
        // C
        'cannot_more_than' => ':resource1 cannot be more than :resource2.',
        'cannot_more_than_chars' => ':resource1 cannot be more than :resource2 characters.',
        'closed_co' => 'Permission Error: Customer Order is Completed.',
        'closed_po' => 'Permission Error: Purchase Order is Completed.',
        'closed_grn' => 'Status must be Open for Goods Receiving Note.',
        'column_cannot_edit' => ':column [:value] cannot edit :resource.',
        'convert_uom' => 'Reversal UOM conversion with factor [:original_factor] found. Conversion factor should be [:reverse_factor]',
        'copickclose' => 'Line status is Completed.',
        'count_sheet_not_exist' => 'There is no count sheet exist.',
        'cust_status_error' => 'The cust status field must be 0[Inactive] or 1[Active]',
        'counterinactive'   => 'Counter [:resource] inactive status',
        'counterinvalid'   => 'Invalid Counter [:resource]',
        'already_in_use' => ':resource already in use',
        'cannot_be_empty' => ':resource cannot be empty',
        'cannot_more_qty_ordered__qty_received_qty_returned'   => 'Quantity Ordered cannot be less than Quantity Received minus Quantity Returned.',
        'co_status_update_1' => 'CO status is not open. ',
        'completed_without_value' => ':resource is completed.',

        //D
        'diffdocnum' => 'You cannot select the same TO with different Document Number',

        // E
        'edit_error_status_open_qty_picked' => 'Cannot edit to Status [Open] with Qty Picked more than 0.',
        'edit_error_status_released_qty_picked' => 'Cannot edit to Status [Released]. Must all have Picker, Due Date, and Packing Location.',
        'edit_qty_on_hand' => 'Not allow to change Lot Tracked for Item with Qty on Hand more than 0.',
        'edit_uom_qty_on_hand' => 'UOM cannot be changed for this item as it has an On Hand > 0 quantity in at least one location.',
        'uom_validation' => "UOM cannot be changed for this item as there are existing PO Lines, CO Lines, Jobs, Job Materials, or Transfer Order Lines associated with it.",
        'endtask' => 'Please :endtask for this employee.',
        'endtaskMachine' => 'Please :endtask for this machine.',
        'Error' => 'Error',
        'error_general' => "Oops! Something went wrong.",
        'error_404' => "The record you attempted to access no longer exists, as it may have been deleted in another session. Please refresh the page to view the latest data.",
        'error_500' => "We are experiencing technical difficulties. Please try again later.",
        'error_exists_item_num' => 'Not allow to use Alternate Barcode same as existing Item',
        'error_exists_alternate_barcode' => 'Not allow to use Item same as existing Alternate Barcode',
        'error_exists' => 'Not allow to delete because item(s) associated with existing order(s) and/or transaction(s).',
        'error_exists_in' => 'Permission Error. :resource1 exists in :resource2.',
        'error_exists_in_with_status' => 'Permission Error. :resource1 exists in :resource2 with Status [:resource3].',

        'error_exists_tolines_in_with_status' => 'TO Number [:resource1] still has item(s) in transit.',
        'error_existsv1' => 'Not allow to delete because :resource1(s) associated with existing :resource2(s).',
        'error_existsv2' => 'Not allow to delete because :resource1(s) associated with existing :resource2(s) and/or :resource3(s).',
        'error_delete' => 'Cannot delete :value :name.',
        'error_delete_exist_emp' => 'Not allow to delete because Employee(s) associated with existing Transaction(s)  and/or Pick List(s) and/or Inventory Count Sheet(s).',
        'error_delete_exist_itemloc' => 'Not allow to delete because Item Location(s) with Qty on Hand more than 0 and/or associated with existing Order(s) and/or Transaction(s)  and/or Allocation(s).',
        'error_delete_transit_loc' => "Not allow to delete because Location(s) associated with existing Warehouse(s)'s Transit Location.",
        'error_delete_exist_loc' => 'Not allow to delete because Location(s) with Item Qty on Hand more than 0 and/or associated with existing Transaction(s) and/or Allocation(s).',
        'error_delete_exist_lot' => 'Not allow to delete because Lot(s) with Qty on Hand more than 0 and/or associated with existing Order(s) and/or Transaction(s)  and/or Allocation(s).',
        'error_delete_exist_lotloc' => 'Not allow to delete because Item Lot Locations(s) with Qty on Hand more than 0 and/or associated with existing Order(s) and/or Transaction(s)  and/or Allocation(s).',
        'error_delete_exist_machine' => 'Not allow to delete because Machine(s) associated with existing Transaction(s).',
        'error_delete_jobroute' => 'Not allow to delete because Job Order(s) status is Released or Completed.',
        'error_delete_jobroute_qty_issue_more_than_zero' => 'Cannot delete Job Routes with Job Materials where quantity issued is more than 0.',
        'error_delete_exist_reasoncode' => 'Not allow to delete because Reason Code(s) associated with existing Transaction(s).',
        'error_delete_exist_task' => 'Not allow to delete because Indirect Task(s) associated with existing Transaction(s).',
        'error_delete_exist_wc' => 'Not allow to delete because Work Center(s) associated with existing Order(s) and/or Transaction(s).',
        'error_delete_exist_whse' => 'Not allow to delete because Warehouse(s) with Item Qty on Hand more than 0 and/or associated with existing Order(s) and/or Transaction(s) and/or location assigned to the warehouse.',
        'error_delete_job_material' => 'Not allow to delete because Job Order(s) status is Released or Completed.',
        'error_delete_jobmaterial_qty_more_than_zero' => 'Cannot delete Jobs with Job Materials where quantity issued is more than 0.',
        'error_delete_jobmaterial_qty_more_than_zero_resource' => 'Cannot delete Job Material [:job_num] where quantity issued is more than 0.',
        'error_delete_allocation' => 'Not allow to delete because Allocation(s) status is Generated or Completed / Picklist Status is not Open.',
        'error_delete_countgroup' => 'Not allow to delete because Count Group(s) associated with existing Item(s).',
        'error_delete_co' => 'Not allow to delete because Customer Order with quantity picked and/or quantity shipped more than 0 and/or status is Completed.',
        'error_delete_cr' => 'Not allow to delete because Customer Return  status is Completed.',
        'error_delete_po' => 'Not allow to delete because Purchase Order with quantity received more than 0 and/or status is Completed.',
        'error_delete_grn' => 'Not allow to delete because GRN with quantity received and/or quantity returned more than 0 and/or status is Completed.',
        'error_delete_to_line' => 'Not allow to delete because Transfer Order Line with quantity shipped more than 0 and/or status is Completed.',
        'error_delete_qty_on_hand' => 'Permission Error. Cannot delete Item with Qty on Hand more than 0.',
        'error_delete_default' => 'Permission Error. Cannot delete default :resource1.',
        'error_delete_trans_order_qty_0_or_more' => 'Not allow to delete because Transfer Order with Qty Shipped more than 0 and/or status is Completed.',
        'error_delete_trans_order_exists' => 'Permission Error. Transfer Order exists in Material Transactions.',
        'error_delete_trans_order' => 'Permission Error. Unable to delete Transfer Order item',
        'error_co_ttl_qty' => 'Total Qty Released for :resource1 is more than Qty Ordered',
        'error_co_change_uom' => 'Permission Error. Cannot change UOM for transacted PO.',
        'error_co_delete_qty_more_than_0' => 'Not allow to delete because customer order line(s) with quantity shipped more than 0 and/or status is Completed.',

        'error_cr_delete_qty_more_than_0' => 'Not allow to delete because return customer line(s) with quantity returned more than 0 and/or status is Completed.',
        'error_co_exists' => 'Permission Error. Customer Order exists in Material Transactions.',
        'error_cr_exists' => 'Permission Error. Return Customer  exists in Material Transactions.',
        'error_change_status_default_whse' => 'Permission Error. Cannot change status to inactive for default warehouse.',
        'error_exists_active' => 'Not allow to inactive because :resource1 associated with existing :resource2(s).',
        'error_exists_active_qtyonhand' => 'Active status cannot be changed for this item as there is an On Hand quantity or it is associated with existing orders or transactions.',
        'error_exists_loc_active_qtyonhand' => 'Not allow to inactive because Location with Item Qty on Hand more than 0 and or associated with existing Order(s) and or default location setting(s).',
        'error_change_loctype' => 'Not allow to change Location Type because Location(s) with Item Qty on Hand more than 0 and or associated with existing Order(s).',
        'error_change_loctype2' => 'Not allow to change Location Type because Location(s) with Item Qty on Hand more than 0 and or associated with existing Order(s) and or default transit location of warehouse.',
        'error_inactive_exist_emp' => 'Not allow to inactive because Employee ID associated with existing Transaction(s)  and or Pick List(s) and or Inventory Count Sheet(s).',
        'error_inactive_exist_machine' => 'Not allow to inactive because Machine ID associated with existing Transaction(s).',
        'error_inactive_exist_wc' => 'Not allow to inactive because Work Center associated with existing Job Route(s) and or Bill of Material(s).',
        'error_inactive_exist_whse' => 'Not allow to inactive because Warehouse(s) with Item Qty on Hand more than 0 and or associated with existing Order(s) and or Transaction(s).',
        'error_inactive_countgroup' => 'Not allow to inactive because Count Group associated with existing Item(s).',
        'error_inactive_warehouse' => 'The Warehouse is inactive.',
        'error_inactive_item' => 'Item is inactive.',
        'error_itemWhse' => 'Not allow to delete because Item Warehouse(s) with Qty on Hand more than 0 and or associated with existing Order(s) and or Transaction(s).',
        'error_notallow_edit' => 'Not allow to edit because :resource1(s) associated with existing :resource2(s).',
        'error_password' => 'The password must be 8–30 characters, and include a number, a symbol, a lower and an upper case letter.',
        'error_password_confirm' => 'The password confirmation does grn_line.',
        'error_qty_on_hand' => 'Permission Error. Cannot inactive Item with Qty on Hand more than 0.',
        'error_resource_delete_qty_on_hand' => 'Permission Error. Cannot delete :resource with Qty on Hand more than 0.',
        'error_receipt_date_and_ship_date' => ':resource1 cannot be earlier than :resource2.',
        'error_username' => 'Invalid username. You can use alphanumeric, period and underscore only.',
        'exists' => ':resource [:name] already exists. Please try another :resource.',
        'bom_exists'=> 'Item [:resource] with revision [:resource2] already exists in Item BOM. Please try another.',
        'exists2' => ':resource [:name] already exists. Please try another :resource number.',
        'exists_without_value' => ':resource already exists. Please try another :resource.',
        'exists_record' => ':resource already exists. Please try another.',
        'exists_combination' => 'UOM Conversion already exists. Please try another UOM Conversion.',
        "exists_object_label"=> "Label Name and Object already exists. Please select a different combination.",
        'exists_update_combination' => 'UOM Conversion cannot update because the UOM Conversion already exists.',
        'error_exist_update_job' => ':resource exist in Material Transactions and/or Job Transactions and/or Machine Transactions.',
        'error_delete_exist_containerItem' => 'Not allow to delete because Container Item(s) associated with existing Records.',
        'exists_lpn_line' => 'The Pallet Line :resource already exists, please use another Pallet Line.',
        'exists_res' => ':resource already exists.',
        'error_delete_exist_uomconv' => 'Not allow to delete because UOM Conversion associated with existing Records.',

        'error_date_format'          => 'Invalid date format for :resource. Valid format should be :format',
        'error_delete_jobroute_qty_more_than_zero' => 'Cannot delete Job Routes with Qty Received, Qty Moved, Qty Completed or Qty Scrapped more than 0.',
        'error_qty_required_cannotlesshan_qty_issued' => 'Qty Required cannot be less than Qty Issued',
        'error_max_suffix' => 'Suffix can not be more than 4 characters.',
        'error_delete_associated' => 'Cannot delete the selected item(s) as they have an On Hand quantity or are associated with existing transactions (PO Lines, CO Lines, Jobs, Job Materials, or Transfer Orders).',
        'error_lot_prefix' => 'Lot Prefix cannot be added because the item is not lot-tracked.',


        'error_update_in_with_status' => 'Not allow to update because Transfer Order Line with quantity shipped more than quantity received',
        'error_update_in_with_status_qty_shipped' => 'Not allow to update Warehouse because Transfer Order Line already exists. ',
        'error_update_in_with_status_to' => 'Not allow to update because Transfer Order Line with quantity shipped more than 0',

        'error_status_open' => 'Status must be Open for Customer Return.',
        // F
        'file_not_exists' => "File not exists",
        'file_no_permission' => "You do not have permission to access this file.",
        //g
        'greaterthan' => ':resource1 for :resource2 must be greater than :number.',
        'greaterthan2' => 'The :resource may not be greater than :number characters.',
        'grn_prefix_not_defined' => ':resource Number Definition has not been set.',
        // I
        'invalid_file_template' => 'The file template is invalid',
        'invalid_cust_num' => 'Inactive customer / Invalid customer',
        'invalid_password_current' => 'The current password is invalid.',
        'invalid_password_new' => 'The new password is invalid.',
        'invalid' => 'Invalid :resource',
        'invalid_format' => 'Invalid file format. Supported file formats: PDF, JPEG, PNG and JPG.',
        'invalid_api_key_title' => 'Connection Test Failed',
        'invalid_uom_job' => 'UOM cannot be changed.',

        'invalid_api_key' => 'The connection test with the provided API Key failed. Please ensure that the API Key is correct and try again.',
        'invalid_json' => 'Invalid request json',
        'importfiletype' => 'The file is invalid.',
        'import_limit' => "Import limit exceeded: Maximum records allowed is :limit",
        'import_empty' => 'Import File is empty, Please ensure that there\'s rows and all required data is filled in appropriately',
        'inactive' => ':resource [:name] is inactive.',
        'inactive_not_allowed' => 'Inactive :resource not allowed.',
        'inactive_without_value' => ':resource is inactive.',
        'incomplete_qty' => 'Incomplete Quantity.',
        'incomplete_qty1' => 'Qty to Receive must be less than or equal to Qty Receivable',
        'invalid_company_email' => 'The company email must be a valid email address.',
        'invalid_count_status' => 'Invalid Count Sheet status.',
        'invalid_email' => 'The email must be a valid email address.',
        'invalid_number' => 'Please enter a valid number.',
        'invalid_phonenum' => 'The phone number format is invalid.',
        'invalid_item_num' => 'Invalid item number.',
        'invalidoperation' => 'Cannot process inactive operation.',
        'is_invalid' => ':resource is invalid.',
        'is_not_counted' => 'Cannot review incomplete count sheet(s).',
        'is_required' => 'This field is required.',
        'is_required2' => 'This field is required',
        'itemlocnotexist' => ':resource does not exist in the location.',
        'item_not_lot_tracked' => 'Item is not Lot Tracked.',
        'item_cannot_same_with_parents' => 'Material cannot be the Parent Item of BOM Item',
        'invalid_sap_setting' => 'SAP Business One connection not set up, this integration cannot be activated.',
        'item_whse_not_exist_in_itemwhse' => 'Item that has Item [:resource1] and Whse [:resource2] does not exist in Item Warehouse.',
        // J
        'job_status_update_1' => 'At least one Job route is required to release status. ',
        'job_status_update_2' => 'Cannot update job status due to material/job/machine transaction transaction found for this job. ',


        'jobnoroute' => 'Status not allow to be Released because Job Order without any Job Route and/or Job Material.',
        'joborderqtymorethanzero' => 'Status not allow to be Open because Job Order with material issued and/or Qty Completed more than 0 and/or is associated with Job Transaction(s).',
        // L
        'lesser_than' => ':resource1 is not allow to be lesser than :resource2.',
        'lessthan' => ':resource1 must be less than :resource2.',
        'lot_definition_missing' => 'Lot Number Definition is missing in Item Master. Please define it before proceeding.',


        'lessthan_equalto' => ':resource must be less than or equal to :value ',
        'lpn_null' => 'LPN can not be empty.',
        'line_still_in_transit' => 'The line cannot update to completed status because item still in transit.',
        // M
        'max_characters' => 'Order Prefix Max 30 characters.',
        'max_user_registered' => 'Failed to update :resource [:name].',
        'morethan' => ':resource must be more than :name.',
        'multiple_active' => 'Not allow to have multiple active same order type.',
        'mustdiffer' => ':resource1 and :resource2 must be different.',
        'maxfile' => 'Max file size is 2MB.',
        'max_phonenum' => 'The phone number may not be greater than 12 characters.',
        'min_phonenum' => 'The phone number must be at least 10 characters.',
        'morethan_one_active' => 'Not allow to have more than one active License Plate Number Definition.',
        'max_lpn_num' => 'Max LPN running number of [:resource] digits reached.',
        'must_lot_tracked' => "'Lot Tracked' unchecked, only 'Location' allowed for 'Issue By'.",
        // N
        'no_balance' => 'Purchase Order Line\'s Qty Balance must be greater than 0.',
        'no_data_available' => 'No data available',
        'no_data_available_in_table' => 'No data available in table',
        'no_item_available' => 'There are no item available for this :resource.',
        'not_updated' => ':resource could not been updated because of some error.',
        'data_not_defined' => ':resource is not defined',
        'no_active_labor_found' => 'Unable to stop labor runs: No active labor runs found or they have already been stopped',

        'notefile' => 'Invalid file type. The importer only supports PDF, JPEG, PNG and JPG file formats.',
        'nolineposted' => 'No count posted.',
        'nosheetreleased' => 'No count sheet released.',
        'nosheetreviewed' => 'No count sheet review.',
        'nosheetresumed' => 'No count sheet resumed.',
        'nosheetsuspended' => 'No count sheet suspended.',
        'not_allow_reduce' => 'Not allow to reduce :attribute.',
        'notexist' => ':resource does not exist.',
        'notexist_notactive' => ':resource does not exist or inactive.',
        'notexist_inactive' => ':resource is inactive.',
        'notexistin' => ':resource1 does not exist in [:name] :resource2.',
        'notexistcontenttype' => 'The mandatory \'Content-Type\' header is missing',
        'notfound' => ':resource [:name] not found.',
        'notlogo' => 'Invalid file format. Supported file formats: GIF, JPEG, PNG and JPG.',
        'not_planned_count_sheet' => 'Count Sheet status is not Planned.',
        'nofilechoose' => 'No file chosen.',
        'no_orderline_selected' => 'There is no selected order line to run allocation.',
        'no_row' => 'No rows added to the list. Please add at least one before proceeding.',

        'no_orderline_allocation' => "Order Item(s) selected with Qty Required [:resource1] must be less than or equal to Qty Available [:resource2]. Please re-select.",
        'not_found' => ":resource not found.",
        'not_found2' => "No :resource found.",

        'notmatchdocnum' => 'Doc Num grn_line with selection.',

        // O
        'over_limit' => 'Start Running No more than Limit running number setting.',
        // P
        'post_qty_empty' => 'Post quantity cannot be empty.',
        'processinactive' => 'Cannot process inactive :resource.',
        'bartender_cloud_credentials' => "Please enter Barender Cloud Client ID and Client Secret in the Site Setting before enabling the integration. These credentials are required to authenticate the connection",
        'bartender_cloud_error' => "We encountered an issue while communicating with BartenderCloud.<br> Error Message: :errorText <br> What to do next:<br> Ensure BartenderCloud is currently operational and not undergoing maintenance.<br> If the problem persists, please contact your BartenderCloud support team for further assistance",
        'print_node_error' => "We encountered an issue while communicating with PrintNode.<br> Error Message: :errorText <br> What to do next:<br> Ensure PrintNode is currently operational and not undergoing maintenance.<br> If the problem persists, please contact your PrintNode support team for further assistance",
        //  Cannot create Item Lot Location for non lot-tracked Items.
        'permission_denied_auto_create_itemwhse' => 'Permission denied: Unable to auto-create Item Warehouse for Item [:resource1] and Whse [:resource2].',
        'permission_error_lot_loc' => 'Permission Error.  Cannot create Item Lot Location for non lot-tracked Items',
        'process_error_qty_less'   => 'Not allow to delete because Item Lot Location(s) with Qty on Hand more than 0 and/or associated with existing Order(s) and/or Transaction(s) and/or Allocation(s).',

        'permission_error_count_batch_closed' => 'Permission Error. Count Batch is Closed',
        'permission_error_cannot_delete_count_batch_closed' => 'Permission Error. Cannot delete Count Batch where Count Sheet status Completed.',
        'permission_error_cannot_delete_count_batch_released' => 'Permission Error. Cannot delete Count Batch when Count Batch status is Released.',
        'permission_error_cannot_change_due_closed_po' => 'Permission Error. Cannot change due date for Closed PO.',
        'permission_error_cannot_change_uom_transacted_order' => 'Permission Error. Cannot change UOM for transacted :resource.',
        'permission_error_cannot_change_item_num_transacted_order' => 'Permission Error. Cannot change Item Num for transacted :resource.',
        'permission_error_cannot_delete_count_batch_reviewed' => 'Permission Error. Cannot delete Count Batch when Count Batch status is Reviewed.',
        'resource_name_value_not_match' => ':resource1\'s :name not match with :resource2\'s :name.',
        'please_add_lot' => 'Please add at least one lot before proceeding.',

        // Q
        'qty_ordered_min' => 'Qty Ordered cannot be less than :resource',
        'qty_released_min' => 'Qty Released cannot be less than :resource',
        'qty_received_more_than_qty_shipped' => 'Qty Received more than Qty Shipped.',
        'qty_shipped_more_than_qty_released' => 'Qty Shipped more than Qty Released.',
        'qty_shipped_more_than_qty_required' => 'Qty Shipped more than Qty Required.',
        'qty_variance_allocate' => "Not allow to add because :resource(s) with Qty Variance more than 0 have been selected. Please re-select.",
        'qty_shortage_allocate' => "Not allow to add because :resource(s) with Qty Shortage more than 0 have been selected. Please re-select.",
        'qty_less_than_with_value' => 'Qty must be less than or equal to Qty Available [:resource1].',

        // R
        'recordnotfound' => 'Record not found.',
        'required' => ':resource is required.',
        'required2' => 'The :resource field is required.',
        'reduce_license' => 'Total license used cannot exceed total purchased license after reduction.',
        'res_status' => 'The res_status field must be 1 (active) or 0 (inactive).',
        'retrieved_successfully' => ':resource retrieved successfully.',
        // S
        'samelocation' => ':resource1 and :resource2 must be different.',
        'sameresource' => 'Please select same :resource.',
        'selectone' => 'Please select at least one record.',
        'select_segment' => 'Please select segment for the definition.',
        'Sorry' => 'Sorry',
        'successfully_listings' => ':resource successfully Listings',
        // T
        'total_weight_out_of_tolerance' => 'Total weight is out of the tolerance range. Please adjust the weights.',
        'total_weight_must_greater_zero' => 'Total weight must be greater than 0',
        // U
        'update_machine_status' => 'Permission Error. Cannot change status to Inactive when machine is running.',
        'usergrouplist_exist' => 'Cannot remove :resource. User is in User Group [:resource2].',
        'uom_null' => 'UOM cannot be empty',
        // V
        'validation_error' => 'Validation Error',
        // W
        'Warning!' => 'Warning',
        'wrong' => 'Wrong :resource.',
        // Z
        'zone_warehouse_error' => 'Zone is not in its warehouse.',

        'zone_add_invalid_warehouse_error' => 'Warehouse doesn\'t exist.'
        //
    ]
];
