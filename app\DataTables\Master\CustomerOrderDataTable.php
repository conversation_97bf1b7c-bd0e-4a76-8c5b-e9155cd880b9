<?php

namespace App\DataTables\Master;

use App\CustomField;
use App\CustomerOrder;
use App\CustomerOrderItem;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Html\Editor\Editor;
use Illuminate\Support\Facades\Cookie;
use Camroncade\Timezone\Facades\Timezone;
use DataTables;

use DB;
use App\SiteSetting;
use App\DataTables\BaseDataTable;
use App\Traits\DataTableOptions;

class CustomerOrderDataTable extends BaseDataTable
{
    use DataTableOptions;

    protected $model = CustomerOrder::class;
    protected $table_name = 'customer_orders';

    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return Datatables::of($query)
            ->editColumn('rel_status', function ($po_item) {
                $status_lists = CustomerOrderItem::rel_status_enum;
                if(array_key_exists($po_item->rel_status, $status_lists)) return $status_lists[$po_item->rel_status];
                return '';
            })
            ->editColumn('due_date', function ($item) {
                $datFormat =   SiteSetting :: select('date_format')->where('site_id',auth()->user()->site_id)->value('date_format');
                return date($datFormat, strtotime($item->due_date));
            })
            ->filterColumn('due_date', function($q,$value) {
                $datFormat =   SiteSetting :: select('date_format')->where('site_id',auth()->user()->site_id)->value('date_format');
                $dueDateFormat = str_replace("/","-", $datFormat);
                $valueFormat = str_replace("/","-", $value);
                $response = strpos($datFormat,"/");
                $dueDate = explode('-',$dueDateFormat);
                $val = explode('-',$valueFormat);

                if($response)
                {
                    $finalDateFormat = '%'.$dueDate[0].'/%'.$dueDate[1].'/%'.$dueDate[2];
                    if(isset($val[2]) && isset($val[1])){
                        $finalVal = $val[0].'%/%'.$val[1].'%/%'.$val[2];
                    }
                    else if(isset($val[1])){
                        $finalVal = $val[0].'%/%'.$val[1];
                    }
                    else{
                        $finalVal = $val[0];
                    }

                }
                else{
                    $finalDateFormat = '%'.$dueDate[0].'-%'.$dueDate[1].'-%'.$dueDate[2];
                    if(isset($val[2]) && isset($val[1])){
                        $finalVal = $val[0].'%-%'.$val[1].'%-%'.$val[2];
                    }
                    else if(isset($val[1])){
                        $finalVal = $val[0].'%-%'.$val[1];
                    }
                    else{
                        $finalVal = $val[0];
                    }
                }
                $co = CustomerOrderItem::where(DB::raw("(DATE_FORMAT(due_date,'".$finalDateFormat."'))"),'LIKE',"%$finalVal%")->pluck('id')->toArray();
                $q->whereIn('id',$co);

            })
            ->addColumn('action', function($coitem) {
                if($coitem->co_line!=""){
                    return "<a href='" . route('editCO',
                            ['co_item' => $coitem->id]
                        ) . "' class='btn btn-icon btn-primary  btn-sm' id='edit' name='edit' title='Edit'><i class='icon-edit'></i></a>"
                        ;
                }
            });
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\CustomerOrder $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(CustomerOrderItem $model)
    {
        $customCOFields = CustomField::whereIn('table_name', ['customer_orders'])->display()->orderBy('id')->get();

        $site_id = auth()->user()->site_id;
        $co_num = $model::select('co_num');

        $selectFields = ['coitems.id',
                        'coitems.due_date',
                        'coitems.co_num',
                        'coitems.co_line',
                        'coitems.item_num',
                        'coitems.item_desc',
                        'coitems.cust_num',
                        'coitems.whse_num',
                        'customer_orders.shipping_zone_code',
                        'coitems.rel_status'];

        $selectFields2 = [ // DB::raw('TO_BASE64(co_num) as id') ,
            DB::raw('0 as id'),
            'due_date',
            'co_num',
            DB::raw('null as co_line'),
            DB::raw('"" as item_num'),
            DB::raw('"" as item_desc'),
            'cust_num',
            DB::raw('"" as whse_num'),
            //DB::raw('"" as shipping_zone'),
            'shipping_zone_code',
            DB::raw('co_status as rel_status') ];

        if($customCOFields){
            foreach ($customCOFields as $customField) {
                $column_name = $customField->table_name.'.'.$customField->column_name;
                $field_name = $customField->table_name.'_'.$customField->column_name;
                array_push($selectFields,DB::raw($column_name.' as '.$field_name));
                array_push($selectFields2,DB::raw($column_name.' as '.$field_name));
            }
        }

        // for export columns
        if ($this->request->isMethod('post'))
        {
            $exportFields1 = [
                'coitems.uom', "coitems.qty_ordered", "coitems.qty_returned", "coitems.qty_shipped", "coitems.qty_required",
                "coitems.qty_allocated", "coitems.qty_shortage", "coitems.qty_picked"
            ];
            $exportFields2 = array_map(function($value) {
                $fieldName = explode("coitems.", $value);
                return DB::raw('"" as '. $fieldName[1]);
            }, $exportFields1);

            $selectFields = array_merge($selectFields, $exportFields1);
            $selectFields2 = array_merge($selectFields2, $exportFields2);
        }

        $query = DB::query()->from('coitems')
        ->select(
            $selectFields
        )
        ->leftJoin('customer_orders', 'customer_orders.co_num','=','coitems.co_num', 'customer_orders.site_id','=','coitems.site_id')
        ->where('coitems.site_id', $site_id);

        $query2 = DB::query()->from('customer_orders')
            ->select(
                $selectFields2
            )
            ->where('site_id', $site_id)
            ->whereNotIn('co_num', $co_num);

        $query->union($query2);
        $query = DB::query()->fromSub($query, "query");
        return $query;
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('CustomerOrders')
            ->addTableClass('nowrap table-bordered table-xs')
            ->parameters([
                'responsive' => true,
                'autoWidth' => false,
                'scrollY' => 400,
                'scrollX' => true,
                'scrollCollapse' => true,
                'colReorder' => [
                    'fixedColumnsLeft' => [1],
                ],
            ])
            ->orderCellsTop(true)
            ->fixedHeader(true)
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Blrtip')
            ->orderBy([ 2, 'desc' ])
            ->buttons(
                // Button::make('export')->className('hidden btn buttons-excel buttons-html5 btn-info')
                Button::make('postExcel')->text('Export to Excel')->className('btn buttons-excel buttons-html5 btn-info')->responsive(false)->autoWidth(false),
                Button::make('postCsv')->text('Export to CSV')->className('btn btn-info')
            );
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        $username = auth()->user()->id."CustomerOrders";
        $value = Cookie::get($username);
        $COArr = json_decode($value);

        $customFields = CustomField::where('table_name','customer_orders')->display()->orderBy('id')->get();

        if(is_array($COArr)){

        }
        else{
            // $COArr = ['id','due_date','co_num','co_line','item_num','item_desc','cust_num','whse_num','shipping_zone','rel_status'];
        }

        $arrData = [
            'id'=>array('title'=> ''),
            'due_date'=>array('title'=>'Due Date'),
            'co_num'=>array('title'=>'CO Number', 'width'=>'15%',
                'render'=>'function() {
                    var url = "'.route('co.showByCoNum', ':co_num').'";
                    url = url.replace(":co_num",btoa(full.co_num));

                    var data = "<a href="+url+"> " + full.co_num + "</a>";
                    return data;
                }'),
            'co_line'=>array('title'=>'Line', 'width'=>'7%',
                'render'=>'function() {
                    if(full.co_line){
                        var url = "'.route('viewCO', ':id').'";
                        url = url.replace(":id",full.id);

                        var data = "<a href="+url+"> " + full.co_line + "</a>";
                        return data;
                    }
                    return "";
                }'),
            'item_num'=>array('title'=>'Item', 'width'=>'10%'),
            'item_desc'=>array('title'=>'Description', 'width'=>'20%'),
            'cust_num'=>array('title'=>'Customer Code'),
            'whse_num'=>array('title'=>'Warehouse'),
            'shipping_zone_code'=>array('title'=>'Shipping Zone'),
            'rel_status'=>array('title'=>'Status', 'width'=>'7%'),
        ];

        // for export columns
        if ($this->request->isMethod('post'))
        {
            $arrData = [
                'id'=>array('title'=> ''),
                'due_date'=>array('title'=>'Due Date'),
                'co_num'=>array('title'=>'CO Number', 'width'=>'15%',
                    'render'=>'function() {
                        var url = "'.route('co.showByCoNum', ':co_num').'";
                        url = url.replace(":co_num",btoa(full.co_num));

                        var data = "<a href="+url+"> " + full.co_num + "</a>";
                        return data;
                    }'),
                'co_line'=>array('title'=>'Line', 'width'=>'7%',
                    'render'=>'function() {
                        if(full.co_line){
                            var url = "'.route('viewCO', ':id').'";
                            url = url.replace(":id",full.id);

                            var data = "<a href="+url+"> " + full.co_line + "</a>";
                            return data;
                        }
                        return "";
                    }'),
                'item_num'=>array('title'=>'Item', 'width'=>'10%'),
                'item_desc'=>array('title'=>'Description', 'width'=>'20%'),
                'uom'=>array('title'=>'UOM'),
                'qty_ordered'=>array('title'=>'Qty Ordered'),
                'qty_returned'=>array('title'=>'Qty Returned'),
                'qty_shipped'=>array('title'=>'Qty Shipped'),
                'qty_required'=>array('title'=>'Qty Required'),
                'qty_allocated'=>array('title'=>'Qty Allocated'),
                'qty_shortage'=>array('title'=>'Qty Shortage'),
                'qty_picked'=>array('title'=>'Qty Picked'),
                'cust_num'=>array('title'=>'Customer Code'),
                'whse_num'=>array('title'=>'Warehouse'),
                'shipping_zone_code'=>array('title'=>'Shipping Zone'),
                'rel_status'=>array('title'=>'Status', 'width'=>'7%'),
            ];
        }

        if(count($customFields) > 0){
            foreach ($customFields as $customField) {
                $column_name = $customField->table_name.'_'.$customField->column_name; // to differentiate btwn table coitems and customer_orders
                $field_name = $customField->field_name;
                // $COArr[count($COArr)] = $column_name;
                $arrData[$column_name] = array("title"=>$field_name);
            }
        }

        //Add Action
        // $countNum = count($COArr);
        // $COArr[$countNum] = 'action';
        $arrData['action'] = array('title'=>'Action','orderable'=>false,'exportable'=>false,'printable'=>false,'width'=>80,'class'=>'text-center');
        // $countTotNum = count($COArr);

        $COArr = array_keys($arrData);

        $index = 0;
        foreach($COArr as $key => $datalocastorage){
            $arrDataFilter[$COArr[$index]] = $arrData[$COArr[$index]];
            $index++;
        }
        $arrCORender=['co_num'=>0, 'co_line'=>1];
        $arrAddWidth=['item_num'=>0, 'item_desc'=>1, 'status'=>2];
        $arrAction= ['action'=>0];
        array_push($arrAction);

        foreach($arrDataFilter as $headertitle => $data)
        {
            if($headertitle!= 'id'){
                $arrRearrange[0] = Column::make('id')
                                        ->title('')
                                        ->addClass('checkbox')
                                        ->orderable(false)
                                        ->searchable(false)
                                        ->exportable(false)
                                        ->printable(false)
                                        ->width('2%')
                                        ->render('"<input name=\"save_value\" type=\"checkbox\" class=\"chk\" value=\"" + full.id + "\"  onChange=\"getValueUsingClass();\">"');
                if(array_key_exists($headertitle,$arrCORender)){
                    $arrRearrange[] = Column::make($headertitle)->title($data['title'])->width($data['width'])->render($data['render']);
                }elseif(array_key_exists($headertitle,$arrAddWidth)){
                    $arrRearrange[] = Column::make($headertitle)->title($data['title'])->width($data['width']);
                }elseif(array_key_exists($headertitle,$arrAction)){
                    $arrRearrange[] = Column::make($headertitle)->title($data['title'])->orderable($data['orderable'])->exportable($data['exportable'])->printable($data['printable'])->width($data['width'])->addClass($data['class']);
                }else{
                    $arrRearrange[] = Column::make($headertitle)->title($data['title']);
                }
            }
        }

        return $arrRearrange;
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'CustomerOrders_' . date('YmdHis');
    }

    /**
     * Get sheetname for export.
     *
     * @return string
     */
    protected function sheetName()
    {
        return 'Customer Orders';
    }
}
