@extends('layout.mobile.app')
@section('title', 'Smart Scan Demo')
@section('content')
<style>
    /* Minimal header for mobile */
    @media (max-width: 576px) {
        .card-header, .card-header.bg-primary {
            padding: 0.3rem 0.8rem;
            font-size: 1rem;
            min-height: 36px;
        }
        .card-header h5 {
            font-size: 1.1rem;
            margin-bottom: 0;
        }
    }
    .fab-scan {
        position: fixed;
        right: 1.5rem;
        bottom: 2.5rem;
        z-index: 9999;
        background: #007bff;
        color: #fff;
        border-radius: 50%;
        width: 56px;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        font-size: 2rem;
        border: none;
        transition: background 0.2s;
    }
    .fab-scan:active, .fab-scan:focus {
        background: #0056b3;
    }
    .scan-toast {
        position: fixed;
        left: 50%;
        bottom: 5rem;
        transform: translateX(-50%);
        background: #007bff;
        color: #fff;
        padding: 0.7rem 1.5rem;
        border-radius: 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        display: none;
        z-index: 10000;
    }
</style>
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="icon-barcode"></i> Smart Scan Stock Move</h5>
    </div>
    <div class="card-body pb-5">
        <form id="smartScanForm" autocomplete="off">
            <div class="mb-3">
                <label for="warehouse" class="form-label">Warehouse</label>
                <input type="text" class="form-control" id="warehouse" name="warehouse" placeholder="Scan or enter warehouse">
            </div>
            <div class="mb-3">
                <label for="item" class="form-label">Item</label>
                <input type="text" class="form-control" id="item" name="item" placeholder="Scan or enter item">
            </div>
            <div class="mb-3">
                <label for="lot" class="form-label">Lot</label>
                <input type="text" class="form-control" id="lot" name="lot" placeholder="Scan or enter lot">
            </div>
            <div class="mb-3">
                <label for="location" class="form-label">Location</label>
                <input type="text" class="form-control" id="location" name="location" placeholder="Scan or enter location">
            </div>
            <div class="mb-3">
                <label for="qty" class="form-label">Quantity</label>
                <input type="number" class="form-control" id="qty" name="qty" placeholder="Scan or enter qty">
            </div>
            <div class="mb-3">
                <label for="remarks" class="form-label">Remarks</label>
                <textarea class="form-control" id="remarks" name="remarks" rows="2"></textarea>
            </div>
            <button type="submit" class="btn btn-primary w-100">Submit</button>
        </form>
    </div>
</div>
<button type="button" class="fab-scan" id="scanBtn" title="Scan QR"><i class="icon-barcode"></i></button>
<div class="scan-toast" id="scanToast">Fields updated from QR!</div>
<script>
    // Simulate QR scan (for demo, replace with real scanner integration)
    document.getElementById('scanBtn').onclick = function() {
        // Example QR string: "WH:W01;ITEM:ABC123;LOT:LOT999;LOC:L01;QTY:10"
        let qr = prompt('Simulate QR scan. Enter QR string:', 'WH:W01;ITEM:ABC123;LOT:LOT999;LOC:L01;QTY:10');
        if (!qr) return;
        let map = {};
        qr.split(';').forEach(pair => {
            let [k,v] = pair.split(':');
            if (k && v) map[k.trim().toUpperCase()] = v.trim();
        });
        if(map['WH']) document.getElementById('warehouse').value = map['WH'];
        if(map['ITEM']) document.getElementById('item').value = map['ITEM'];
        if(map['LOT']) document.getElementById('lot').value = map['LOT'];
        if(map['LOC']) document.getElementById('location').value = map['LOC'];
        if(map['QTY']) document.getElementById('qty').value = map['QTY'];
        // Show toast
        let toast = document.getElementById('scanToast');
        toast.style.display = 'block';
        setTimeout(()=>toast.style.display='none', 2000);
    };
</script>
@endsection
