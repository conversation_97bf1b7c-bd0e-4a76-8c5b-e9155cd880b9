<?php

namespace App;

use Carbon\Carbon;
use App\SiteSetting;
use Illuminate\Database\Eloquent\Model;

class AllocationLocation extends BaseModel
{
    protected $guarded = [];
    protected $table = 'allocation_locations';

    public function allocation() {
        return $this->belongsTo('App\Allocation');
    }

    public function getExpiryDateAttribute($value)
    {
        if ($value && $value != '0000-00-00 00:00:00')
            return Carbon::parse($value)->format(SiteSetting::getOutputDateFormat());
        return NULL;
    }

    public function getFirstReceivedDateAttribute($value)
    {
        if ($value && $value != '0000-00-00 00:00:00')
            return Carbon::parse($value)->format(SiteSetting::getOutputDateFormat());
        return NULL;
    }
}
